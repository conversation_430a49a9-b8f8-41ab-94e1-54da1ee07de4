﻿using AppoMobi.Forms.Content.Camera.Controls;
using AppoMobi.Forms.Content.Camera.Extensions;
using AppoMobi.Forms.Content.Camera.Models;
using AVFoundation;
using CoreAnimation;
using CoreFoundation;
using CoreGraphics;
using CoreImage;
using CoreMedia;
using CoreVideo;
using Foundation;
using ImageIO;
using Metal;
using Photos;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Timers;
using UIKit;
using Vision;
using CGImageProperties = ImageIO.CGImageProperties;
using Color = Microsoft.Maui.Graphics.Color;
using DrawnUi.Camera;

namespace AppoMobi.iOS.Camera
{
    public partial class CameraProcessorViewMetal : ProcessorViewMetal, INotifyPropertyChanged, IDisposable
    //, ICameraPreview
    {
        #region EXPOSURE

        /// <summary>
        /// Measures actual scene brightness using iOS camera's auto exposure system
        /// </summary>
        public async Task<BrightnessResult> MeasureSceneBrightness(MeteringMode meteringMode)
        {
            try
            {
                if (CaptureDevice == null)
                    return new BrightnessResult { Success = false, ErrorMessage = "Camera not initialized" };

                // Lock for configuration
                NSError error;
                if (!CaptureDevice.LockForConfiguration(out error))
                    return new BrightnessResult { Success = false, ErrorMessage = error?.LocalizedDescription };

                // Set metering mode first
                switch (meteringMode)
                {
                    case MeteringMode.Spot:
                        // Set focus/exposure point to center for spot metering
                        var centerPoint = new CGPoint(0.5, 0.5);
                        if (CaptureDevice.ExposurePointOfInterestSupported)
                        {
                            CaptureDevice.ExposurePointOfInterest = centerPoint;
                        }
                        break;

                    case MeteringMode.CenterWeighted:
                        // Reset to default (center-weighted is usually the default)
                        if (CaptureDevice.ExposurePointOfInterestSupported)
                        {
                            CaptureDevice.ExposurePointOfInterest = new CGPoint(0.5, 0.5);
                        }
                        break;
                }

                // Set to AUTO exposure mode to let camera measure the scene
                if (CaptureDevice.IsExposureModeSupported(AVCaptureExposureMode.AutoExpose))
                {
                    CaptureDevice.ExposureMode = AVCaptureExposureMode.AutoExpose;
                }
                else if (CaptureDevice.IsExposureModeSupported(AVCaptureExposureMode.ContinuousAutoExposure))
                {
                    CaptureDevice.ExposureMode = AVCaptureExposureMode.ContinuousAutoExposure;
                }
                else
                {
                    CaptureDevice.UnlockForConfiguration();
                    return new BrightnessResult { Success = false, ErrorMessage = "Auto exposure not supported" };
                }

                CaptureDevice.UnlockForConfiguration();

                // Wait for camera to measure and adjust (important!)
                await Task.Delay(1000);

                // Now read what the camera decided
                var measuredDuration = CaptureDevice.ExposureDuration.Seconds;
                var measuredISO = CaptureDevice.ISO;
                var measuredAperture = CaptureDevice.LensAperture;

                // Calculate the EV that the camera chose for "proper" exposure
                var chosenEV = Math.Log2((measuredAperture * measuredAperture) / measuredDuration) + Math.Log2(measuredISO / 100.0);

                // Convert EV to scene brightness (lux)
                // Formula: Lux = K * 2^EV / (ISO/100)
                // K ≈ 12.5 for reflected light (camera's built-in meter measures reflected light)
                const double K = 12.5;
                var sceneBrightness = K * Math.Pow(2, chosenEV) / (measuredISO / 100.0);

                System.Diagnostics.Debug.WriteLine($"[iOS CAMERA] Measured: f/{measuredAperture:F1}, 1/{(1 / measuredDuration):F0}, ISO{measuredISO:F0}");
                System.Diagnostics.Debug.WriteLine($"[iOS CAMERA] Calculated EV: {chosenEV:F1}, Scene brightness: {sceneBrightness:F0} lux");

                return new BrightnessResult
                {
                    Success = true,
                    Brightness = sceneBrightness
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[iOS CAMERA ERROR] {ex.Message}");
                return new BrightnessResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        public async Task<ExposureResult> MeasureExposure(
            double shutterSpeed,
            double iso,
            double aperture,
            double exposureCompensation,
            MeteringMode meteringMode)
        {
            try
            {
                if (CaptureDevice == null)
                    return new ExposureResult { Success = false, ErrorMessage = "Camera not initialized" };

                // Lock for configuration
                NSError error;
                if (!CaptureDevice.LockForConfiguration(out error))
                    return new ExposureResult { Success = false, ErrorMessage = error?.LocalizedDescription };

                // Check if device supports custom exposure
                if (!CaptureDevice.IsExposureModeSupported(AVCaptureExposureMode.Custom))
                {
                    CaptureDevice.UnlockForConfiguration();
                    return new ExposureResult { Success = false, ErrorMessage = "Custom exposure not supported" };
                }

                // Set exposure mode to custom
                CaptureDevice.ExposureMode = AVCaptureExposureMode.Custom;

                // Convert to CMTime for iOS
                var duration = new CMTime((long)(shutterSpeed * 1000000), 1000000); // microseconds

                // Set custom exposure using LockExposure method
                CaptureDevice.LockExposure(duration, (float)iso, (CMTime syncTime) =>
                {
                    // This is called when the exposure is set
                });

                // Set exposure compensation (target bias)
                if (CaptureDevice.MaxExposureTargetBias > 0 && exposureCompensation != 0)
                {
                    var bias = (float)exposureCompensation;
                    // Clamp to device's supported range
                    bias = Math.Max(CaptureDevice.MinExposureTargetBias,
                        Math.Min(CaptureDevice.MaxExposureTargetBias, bias));

                    CaptureDevice.SetExposureTargetBias(bias, null);
                }

                // Set metering mode (point of interest)
                switch (meteringMode)
                {
                    case MeteringMode.Spot:
                        // Set focus/exposure point to center for spot metering
                        var centerPoint = new CGPoint(0.5, 0.5);
                        if (CaptureDevice.ExposurePointOfInterestSupported)
                        {
                            CaptureDevice.ExposurePointOfInterest = centerPoint;
                        }

                        if (CaptureDevice.FocusPointOfInterestSupported)
                        {
                            CaptureDevice.FocusPointOfInterest = centerPoint;
                            CaptureDevice.FocusMode = AVCaptureFocusMode.AutoFocus;
                        }

                        break;
                    case MeteringMode.CenterWeighted:
                        // Reset to default (center-weighted is usually the default)
                        if (CaptureDevice.ExposurePointOfInterestSupported)
                        {
                            CaptureDevice.ExposurePointOfInterest = new CGPoint(0.5, 0.5);
                        }

                        break;
                }

                CaptureDevice.UnlockForConfiguration();

                // Wait a bit for the exposure to settle
                await Task.Delay(500);

                // Get actual values after metering
                var actualDuration = CaptureDevice.ExposureDuration;
                var actualISO = CaptureDevice.ISO;
                var actualAperture = CaptureDevice.LensAperture;

                // Calculate EV (Exposure Value)
                double ev = Math.Log2((aperture * aperture) / shutterSpeed) + Math.Log2(iso / 100.0);

                // Get current exposure target offset (shows if scene is over/under exposed)
                float currentExposureOffset = CaptureDevice.ExposureTargetOffset;

                return new ExposureResult
                {
                    Success = true,
                    ExposureValue = ev,
                    Brightness = currentExposureOffset, // Negative means underexposed, positive means overexposed
                    SuggestedShutterSpeed = actualDuration.Seconds,
                    SuggestedIso = actualISO,
                    SuggestedAperture = actualAperture
                };
            }
            catch (Exception ex)
            {
                return new ExposureResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        #endregion

        protected CVMetalTextureCache _textureCache;

        public void Setup()
        {
            this.Opaque = true;
            SampleCount = 2;

            try
            {
                //                session.StartRunning();

                //metal
                CommandQueue = Device.CreateCommandQueue();
                textureCache = new CVMetalTextureCache(Device);


                CameraCapture = new CameraCapture(textureCache, (cameraUnit) =>
                {
                    var info = _deviceInput.Device.ActiveFormat;
                    var focal = cameraUnit.FocalLength;

                    var pixelsZoom = info.VideoZoomFactorUpscaleThreshold;

                    float aspectH = cameraUnit.PixelXDimension / cameraUnit.PixelYDimension;
                    float aspectV = 1.0f;

                    float fovH = info.VideoFieldOfView;
                    float fovV = fovH / aspectH;

                    //var sensorWidth = (float)(2 * cameraUnit.FocalLength * Math.Tan(fovH / 2.0f));
                    //var sensorHeight = (float)(2 * cameraUnit.FocalLength * Math.Tan(fovV / 2.0f));

                    var sensorWidth = (float)(2 * cameraUnit.FocalLength * Math.Tan(fovH * Math.PI / 2.0f * 180));
                    var sensorHeight = (float)(2 * cameraUnit.FocalLength * Math.Tan(fovV * Math.PI / 2.0f * 180));


                    var check1 = cameraUnit.SensorCropFactor;

                    cameraUnit.SensorHeight = sensorHeight;
                    cameraUnit.SensorWidth = sensorWidth;

                    var check2 = Calculate.CropFactor(sensorWidth, sensorHeight);


                    cameraUnit.FieldOfView = fovH;

                    //if (!_options.NeedCalibration && this.Camera == null)
                    //{

                    //    cameraUnit.FieldOfView = fov;

                    //    //var diagonal = 2 * cameraUnit.FocalLength * Math.Tan(fov / 2.0f);
                    //    //cameraUnit.SensorCropFactor = Calculate.CropFactor(diagonal);

                    //    this.Camera = cameraUnit;
                    //}
                });

                this.CameraCapture.OnFrameCaptured = (pixelBuffer) =>
                {
                    //detect orientation - required
                    if (videoDataOutput.Connections.Any())
                    {
                        var maybe = videoDataOutput.Connections[0].VideoOrientation;
                        if (maybe != _videoOrientation)
                        {
                            _videoOrientation = maybe;
                            UpdateDetectOrientation();
                        }
                    }
                };

                maxInflightBuffers = GetCompatibleBuffers();

                metalSemaphore = new Semaphore(maxInflightBuffers, maxInflightBuffers);

                //camera
                SetupHardware();

                //go
                if (_timer == null)
                {
                    _timer = new System.Timers.Timer();
                    _timer.Interval = 1000;
                    _timer.Elapsed += OnTimerEverySecond;
                    _timer.Start();
                }

                State = CameraProcessorState.Enabled;
                Debug.WriteLine("[CAMERA] Run");


                //                EncodeKernelParameters();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                Stop();
                State = CameraProcessorState.Error;
            }
        }

        private IMTLTexture _captured;

        public new void Dispose()
        {
            CameraCapture?.Dispose();

            base.Dispose();
        }

        public void RaiseProperties()
        {
            OnPropertyChanged("FocalLength");
            OnPropertyChanged("ZoomScale");
            OnPropertyChanged("CaptureWidth");
            OnPropertyChanged("CaptureHeight");
            OnPropertyChanged("ViewportScale");
            OnPropertyChanged("PreviewWidth");
            OnPropertyChanged("PreviewHeight");
            OnPropertyChanged("SavedFilename");
            OnPropertyChanged("Camera");
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;
        //        public event EventHandler<PropertyChangedEventArgs> PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            var changed = PropertyChanged;
            if (changed == null)
                return;

            changed.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        protected CameraOptions _options;

        public CameraProcessorViewMetal(AppoMobi.iOS.Camera.CameraPreviewRenderer renderer, CameraOptions options,
            CGRect frame, IMTLDevice device) : base(frame, device, options.Effect)
        {
            _options = options;
            _renderer = renderer;


            //Initialize();
            Setup();
        }

        public override void LayoutSubviews()
        {
            Debug.WriteLine($"LayoutSubViews requested: {this}");

            UpdateOrientationFromMainThread();

            base.LayoutSubviews();
        }

        public void UpdateOrientationFromMainThread()
        {
            _uiOrientation = UIApplication.SharedApplication.StatusBarOrientation;

            _deviceOrientation = UIDevice.CurrentDevice.Orientation;

            UpdateDetectOrientation();
        }

        //CGFloat is mapped to nfloat
        public UIColor? GetColor(IMTLTexture texture, nfloat x, nfloat myY)
        {
            UIColor? result = null;
            try
            {
                var target = this.CurrentDrawable.Texture;

                var textureScale = (nfloat)(target.Width) / this.Bounds.Width;
                var bytesPerRow = target.Width * 4;
                var y = this.Bounds.Height - myY;

                var twoD = MTLRegion.Create2D((nuint)(x * textureScale), (nuint)(y * textureScale), 1, 1);

                //    var resultHalfArray = Enumerable.Repeat((ushort)6, 12).ToArray();
                // bgra
                var pixel = new int[] { 0, 0, 0, 0 };
                var handlePixel = GCHandle.Alloc(pixel, GCHandleType.Pinned);
                var intPtr = handlePixel.AddrOfPinnedObject();

                target.GetBytes(intPtr, bytesPerRow: bytesPerRow, twoD, 0);

                Marshal.Copy(intPtr, pixel, 0, 4);

                nfloat red = (nfloat)(pixel[2] / 255.0);
                nfloat green = (nfloat)(pixel[1] / 255.0);
                nfloat blue = (nfloat)(pixel[0] / 255.0);
                nfloat alpha = (nfloat)(pixel[3] / 255.0);

                BlackColor = new nfloat[] { 0f, 0f, 0f, 0f };
                BlackColor[0] = red;
                BlackColor[1] = green;
                BlackColor[2] = blue;
                BlackColor[3] = alpha;

                var color = new UIColor(red: red, green: green, blue: blue, alpha: alpha);

                Debug.WriteLine(
                    $"Got color {color} at {(nuint)(x * textureScale)},{(nuint)(y * textureScale)} in image {target.Width}x{target.Height}");


                result = color;
            }
            catch (Exception e)
            {
            }

            return result;
        }

        //public CGPoint? TexturePointForView(CGPoint point)
        //{
        //    CGPoint? result=null;
        //    try
        //    {
        //        var transform = textureTranform;

        //        var transformPoint = transform.TransformPoint(point);

        //        var check = new CGRect(CGPoint.Empty, new CGSize(width: textureWidth, height: textureHeight));
        //        if (check.Contains(transformPoint))
        //        {
        //            result = transformPoint; 
        //        }
        //        else 
        //        {
        //            Debug.WriteLine($"Invalid point {point} result point {transformPoint}");
        //        }
        //    }
        //    catch (Exception e)
        //    { 
        //        Debug.WriteLine(e.ToString()); 
        //    } 

        //    return result; 
        //}

        #region METAL

        // Renderer.

        /// <summary>
        /// Typically, you create one or more command queues when your app launches and then keep those queues around throughout the lifetime of your app.
        /// </summary>
        protected IMTLCommandQueue CommandQueue { get; set; }

        //        protected const CVPixelFormatType pixelFormat = CVPixelFormatType.CV32BGRA; ///CVPixelFormatType.CV420YpCbCr8BiPlanarFullRange
        //protected const CVPixelFormatType pixelFormat = CVPixelFormatType.CV420YpCbCr8BiPlanarFullRange; ///CVPixelFormatType.
        protected const CVPixelFormatType pixelFormat = CVPixelFormatType.CV32BGRA;

        ///CVPixelFormatType.
        protected CameraProcessorState _state;

        public CameraProcessorState State
        {
            get { return _state; }
            set
            {
                if (value != _state)
                {
                    _state = value;
                    CameraCapture.State = value;
                }
            }
        }

        #endregion

        protected CGSize bufferSize = CGSize.Empty;
        AVCaptureSession session = new AVCaptureSession();

        //  AVCaptureVideoPreviewLayer previewLayer = null;
        AVCaptureVideoDataOutput videoDataOutput = new AVCaptureVideoDataOutput();
        AVCaptureDepthDataOutput depthDataOutput = new AVCaptureDepthDataOutput();
        AVCaptureStillImageOutput stillImageOutput;
        NSDictionary options = new NSDictionary();
        CALayer detectionOverlay = null;

        // Vision parts
        VNRequest[] requests;
        protected CVMetalTextureCache textureCache;

        // Flash the screen to signal that AVCamFilter took a photo. BLINK
        public void FlashScreen(double duration = 0.25)
        {
            var flashView = new UIView(frame: this.Frame);
            this.AddSubview(flashView);
            flashView.BackgroundColor = UIColor.White;
            flashView.Layer.Opacity = 1;
            UIView.Animate(duration, () => { flashView.Layer.Opacity = 0; },
                () => { flashView.RemoveFromSuperview(); });
        }

        #region CameraCapture

        AVCaptureDeviceInput _deviceInput;

        protected CGImagePropertyOrientation ExifOrientationFromDeviceOrientation()
        {
            UIDeviceOrientation curDeviceOrientation = UIDevice.CurrentDevice.Orientation;
            CGImagePropertyOrientation exifOrientation;

            switch (curDeviceOrientation)
            {
                case UIDeviceOrientation.PortraitUpsideDown:
                    exifOrientation = CGImagePropertyOrientation.Left;
                    break;
                case UIDeviceOrientation.LandscapeLeft:
                    exifOrientation = CGImagePropertyOrientation.UpMirrored;
                    break;
                case UIDeviceOrientation.LandscapeRight:
                    exifOrientation = CGImagePropertyOrientation.Up;
                    break;
                case UIDeviceOrientation.Portrait:
                    exifOrientation = CGImagePropertyOrientation.Left;
                    break;
                default:
                    exifOrientation = CGImagePropertyOrientation.Left;
                    break;
            }

            return exifOrientation;
        }

        public AVCaptureDevice GetCameraForOrientation(AVCaptureDevicePosition orientation)
        {
            var devices = AVCaptureDevice.DevicesWithMediaType(AVMediaTypes.Video.GetConstant());

            foreach (var device in devices)
            {
                if (device.Position == orientation)
                {
                    return device;
                }
            }

            return null;
        }

        bool alreadySet = false;
        protected CGImagePropertyOrientation exifOrientation;
        CIAffineTransform rotateTransform;
        CIAffineTransform cropTransform;

        /// <summary>
        /// Other filters (contrast, dilation, threshold, etc.) could be added, although it's not clear if 
        /// doing so benefits Vision / CoreML object recognition / tracking
        /// </summary>
        CIEdges edgeDetector;

        CVPixelBuffer resultBuffer;
        protected UIImage _sourceImage;

        #endregion

        #region INTERACTION

        public void Pause()
        {
            if (!_hardwareOk)
                return;

            session.StopRunning();
            State = CameraProcessorState.Paused;
            pixelBuffer?.Dispose();
            pixelBuffer = null;

            CameraCapture.ResetCapture();

            if (_timer != null)
            {
                _timer.Stop();
                _timer.Elapsed -= OnTimerEverySecond;
                _timer = null;
            }

            FPS = 0;

            Debug.WriteLine("[CAMERA] Pause");
        }

        public void Resume()
        {
            if (!_hardwareOk)
                return;

            CameraCapture.ResetCapture();

            session.StartRunning();

            State = CameraProcessorState.Enabled;

            if (_timer == null)
            {
                _timer = new System.Timers.Timer();
                _timer.Interval = 1000;
                _timer.Elapsed += OnTimerEverySecond;
                _timer.Start();
            }

            Debug.WriteLine("[CAMERA] Resume");
        }

        public void SetBackgroundColor(Color formsColor)
        {
            if (formsColor != null)
                this.ClearColor =
                    new MTLClearColor(formsColor.Red, formsColor.Green, formsColor.Blue, formsColor.Alpha);
        }

        /// <summary>
        /// This is intended to obtain correct exif data
        /// to get the 35mm focal length
        /// that on some device we fail to obtain with exif coming from video stream
        /// ex: video stream iphone 6 reporting 94mm, while taking photo reports 29mm
        /// </summary>
        public async void TakeCalibrationPhoto()
        {
            try
            {
                var videoConnection = stillImageOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());

                //var fileName="calibrate.wav";
                //string sFilePath = NSBundle.MainBundle.PathForResource("calibrate", "wav");
                //NSUrl url = NSUrl.FromString(sFilePath);
                //var _player = AVAudioPlayer.FromUrl(url);
                //_player.FinishedPlaying += (object sender, AVStatusEventArgs e) =>
                //{
                //    _player = null;
                //};
                //_player.Play();

                var sampleBuffer = await stillImageOutput.CaptureStillImageTaskAsync(videoConnection);

                var attachments = sampleBuffer.GetAttachments(CMAttachmentMode.ShouldPropagate);

                foreach (var item in attachments)
                {
                    Debug.WriteLine($"{item.Key}: {item.Value}");
                }

                var focals = new List<float>();

                var exif = attachments["{Exif}"] as NSDictionary;
                // 35mmFocal / PhisicalFocalLength = cropFactor;

                var focal35mm = exif["FocalLenIn35mmFilm"].ToString().ToFloat();
                var focal = exif["FocalLength"].ToString().ToFloat();
                var name = exif["LensModel"].ToString();
                var lenses = exif["LensSpecification "] as NSDictionary;

                if (lenses != null)
                {
                    foreach (var lens in lenses)
                    {
                        var add = lens.ToString().ToDouble();
                        focals.Add((float)add);
                    }
                }
                else
                {
                    focals.Add((float)focal);
                }

                //FOV = 2 arctan (x / (2 f)), where x is the diagonal of the film.

                var unit = new CameraUnit
                {
                    Id = name,
                    SensorCropFactor = focal35mm / focal,
                    FocalLengths = focals,
                };

                unit.FocalLength = focal;

                this.Camera = unit;
            }
            catch (Exception e)
            {
            }
        }

        public static int GetCompatibleBuffers()
        {
            return 1;

            string modelIdentifier = UIDevice.CurrentDevice.Model;

            if (UIDevice.CurrentDevice.UserInterfaceIdiom == UIUserInterfaceIdiom.Phone)
            {
                //if (IsModernIPhone(modelIdentifier))
                return 1;
            }
            else
            {
                if (IsModernIPad(modelIdentifier))
                    return 3;
            }

            return 1;
        }

        public static bool IsModernIPad(string modelName)
        {
            if (modelName.Contains("iPadPro") || modelName.CompareTo("iPad13,4") >= 0)
            {
                // This is an iPad Pro or model above and including 13,4
                return true;
            }

            // This is not an iPad Pro or model above and including 13,4
            return false;
        }

        public static bool IsModernIPhone(string modelName)
        {
            if (modelName.StartsWith("iPhone"))
            {
                // Get the device's major iOS version number (e.g. 11 for iOS 11)
                int iosVersion = int.Parse(UIDevice.CurrentDevice.SystemVersion.Split('.')[0]);

                // Check if the device is an iPhone 8 or later
                if (modelName.CompareTo("iPhone10,") >= 0 || (modelName.CompareTo("iPhone9,") >= 0 && iosVersion >= 11))
                {
                    // This is an iPhone 8 or later
                    return true;
                }
            }

            // This is not an iPhone 8 or later
            return false;
        }

        public void Stop()
        {
            Pause();
            State = CameraProcessorState.None;
            textureCache.Flush(CVOptionFlags.None);
            Debug.WriteLine("[CAMERA] Stop");
        }

        //public void TakePicture()
        //{
        //    NeedCaptureImage = true;
        //}

        //protected bool NeedCaptureImage { get; set; }

        //protected bool _metalTakePicture { get; set; }

        #endregion

        public void ChangeConfiguration(CameraOptions options)
        {
            //InvokeOnMainThread(() =>
            //{
            //    FlashScreen(1.2);
            //});

            Stop();

            _options = options;

            SetupHardware();

            Thread.Sleep(1000);

            Resume();
        }

        public AVCaptureDeviceInput Input
        {
            get { return _deviceInput; }
            set
            {
                if (value != _deviceInput)
                {
                    _deviceInput = value;
                    OnPropertyChanged();
                }
            }
        }

        public AVCaptureDevice CaptureDevice
        {
            get
            {
                if (Input == null)
                    return null;

                return Input.Device;
            }
        }

        #region ZOOM

        private void SetCameraLensZoom(nfloat manualZoomCamera)
        {
            _deviceInput.Device.LockForConfiguration(out NSError lerror);
            if (lerror == null)
            {
                _deviceInput.Device.VideoZoomFactor = manualZoomCamera;
                _deviceInput.Device.UnlockForConfiguration();
            }
            else
            {
                Debug.WriteLine($"Cannot lock configuration");
            }
        }

        public void SetZoom(float zoom, bool manual = false)
        {
            //if (manual)
            //    mScaleListener.ScaleFactor = zoom;

            ZoomScale = zoom;
        }

        private bool _lockZoomScale;
        private float _ZoomScale = 1.0f;

        public float ZoomScale
        {
            get { return _ZoomScale; }
            set
            {
                if (_lockZoomScale)
                    return;

                var rounded = (float)Math.Round(value, 2);

                if (_ZoomScale != rounded)
                {
                    _lockZoomScale = true;

                    _ZoomScale = rounded;
                    _manualZoom = rounded;
                    OnPropertyChanged();

                    if (_deviceInput != null)
                    {
                        if (_manualZoom >= _deviceInput.Device.MinAvailableVideoZoomFactor
                            && _manualZoom <= _deviceInput.Device.MaxAvailableVideoZoomFactor)
                        {
                            //zoom camera
                            _manualZoomCamera = _manualZoom;
                            ZoomScaleTexture = 1.0f;
                            //_lastTextureZoom = _manualZoomTexture;
                            //Debug.WriteLine($"Zooming with lens: {rounded}");
                        }
                        else
                        {
                            //zoom texture
                            ZoomScaleTexture = _manualZoom;
                            _manualZoomCamera = 1.0f;
                            //Debug.WriteLine($"Zooming with texture: {rounded}");
                        }

                        if (_manualZoomCamera != _deviceInput.Device.VideoZoomFactor)
                        {
                            //use lens zoom
                            SetCameraLensZoom(_manualZoomCamera);
                        }

                        //Debug.WriteLine($"Zoom {_manualZoom}, lens {_manualZoomCamera}, text {_manualZoomTexture}");
                        _lockZoomScale = false;
                    }
                }
            }
        }

        public nfloat _manualZoomTexture = 1.0f;

        public nfloat ZoomScaleTexture
        {
            get { return _manualZoomTexture; }
            set
            {
                if (_manualZoomTexture != value)
                {
                    _manualZoomTexture = value;
                    OnPropertyChanged();
                }
            }
        }

        public nfloat _manualZoom = 1.0f;
        public nfloat _manualZoomCamera = 1.0f;
        public nfloat _minZoom = 0.1f;

        public void OnZoomPinch(UIPinchGestureRecognizer zoomGesture)
        {
            if (_deviceInput == null)
                return;

            switch (zoomGesture.State)
            {
                case UIGestureRecognizerState.Began:
                    if (_manualZoom >= _deviceInput.Device.MinAvailableVideoZoomFactor
                        && _manualZoom <= _deviceInput.Device.MaxAvailableVideoZoomFactor)
                    {
                        //sync with camera
                        _manualZoom = _deviceInput.Device.VideoZoomFactor;
                    }

                    //sync
                    zoomGesture.Scale = _manualZoom;
                    break;

                case UIGestureRecognizerState.Changed:
                    if (zoomGesture.Scale < _minZoom)
                    {
                        zoomGesture.Scale = _minZoom;
                        break;
                    }

                    ZoomScale = (float)zoomGesture.Scale;
                    break;

                default:
                    break;
            }
        }

        public void OnZoomPinchLegacy(UIPinchGestureRecognizer zoomGesture)
        {
            if (_deviceInput == null)
                return;

            double PinchVelocityDividerFactor(nfloat velocity)
            {
                var factor = 10.0;
                if (velocity < 0)
                    factor = 5.0;
                return factor;
            }

            _deviceInput.Device.LockForConfiguration(out NSError lerror);

            if (lerror == null)
            {
                var desiredZoomFactor = _deviceInput.Device.VideoZoomFactor + Math.Atan2(zoomGesture.Velocity,
                    PinchVelocityDividerFactor(zoomGesture.Velocity));
                // Check if desiredZoomFactor fits required range from 1.0 to activeFormat.videoMaxZoomFactor
                var maxFactor = Math.Min(10, _deviceInput.Device.ActiveFormat.VideoMaxZoomFactor);
                var zoom = (nfloat)Math.Max(1.0, Math.Min(desiredZoomFactor, maxFactor));
                Debug.WriteLine($"Zoom {zoom}");
                _deviceInput.Device.VideoZoomFactor = zoom;

                _deviceInput.Device.UnlockForConfiguration();
            }
            else
            {
                Debug.WriteLine($"Cannot lock configuration");
            }
        }

        #endregion

        #region CAPTURE DEVICE

        private CameraUnit _camera;

        public CameraUnit Camera
        {
            get { return _camera; }
            set
            {
                if (_camera != value)
                {
                    _camera = value;
                    if (value != null)
                    {
                        FocalLength = (float)(value.FocalLength * value.SensorCropFactor);
                    }

                    OnPropertyChanged();
                }
            }
        }

        private int _CaptureWidth;

        public int CaptureWidth
        {
            get { return _CaptureWidth; }
            set
            {
                if (_CaptureWidth != value)
                {
                    _CaptureWidth = value;
                    OnPropertyChanged("CaptureWidth");
                }
            }
        }

        private int _CaptureHeight;

        public int CaptureHeight
        {
            get { return _CaptureHeight; }
            set
            {
                if (_CaptureHeight != value)
                {
                    _CaptureHeight = value;
                    OnPropertyChanged("CaptureHeight");
                }
            }
        }

        private float _focalLength = 0.0f;

        public float FocalLength
        {
            get { return _focalLength; }
            set
            {
                _focalLength = value;
                OnPropertyChanged();
            }
        }

        private bool _hardwareOk;

        public void SetupHardware()
        {
            _hardwareOk = false;

            session.BeginConfiguration();

            if (DeviceInfo.Idiom == DeviceIdiom.Tablet)
            {
                session.SessionPreset = AVCaptureSession.PresetHigh;
            }
            else
            {
                var iSiPhone5sOrLower = UIDevice.CurrentDevice.Model.Contains("iPhone") &&
                                        UIDevice.CurrentDevice.SystemVersion.StartsWith("7.");
                if (iSiPhone5sOrLower)
                {
                    session.SessionPreset = AVCaptureSession.PresetLow;
                }
                else
                {
                    session.SessionPreset = AVCaptureSession.PresetInputPriority;
                }
            }

            var cameraPosition = (_options.Position == CameraPosition.Selfie)
                ? AVCaptureDevicePosition.Front
                : AVCaptureDevicePosition.Back;

            AVCaptureDevice videoDevice = null;

            //todo fix https://github.com/taublast/AppoMobi.ArtOfFoto/issues/1
            //todo fix issue with ZOOM on iPhone 12mini while working on  iPhone 14Pro Max
            if (UIDevice.CurrentDevice.CheckSystemVersion(13, 0)) 
            {
                if (_options.Type == CameraType.Max)
                {
                    videoDevice = AVCaptureDevice.GetDefaultDevice(AVCaptureDeviceType.BuiltInTripleCamera,
                        AVMediaTypes.Video, cameraPosition);
                }
            }

            if (videoDevice == null || _options.Type != CameraType.Max)
            {
                if (_options.Type == CameraType.Max && UIDevice.CurrentDevice.CheckSystemVersion(10, 2))
                {
                    videoDevice = AVCaptureDevice.GetDefaultDevice(AVCaptureDeviceType.BuiltInDualCamera,
                        AVMediaTypes.Video, cameraPosition);
                }

                if (videoDevice == null)
                {
                    var videoDevices = AVCaptureDevice.DevicesWithMediaType(AVMediaTypes.Video.GetConstant());

                    videoDevice = videoDevices.FirstOrDefault(d => d.Position == cameraPosition);
                    if (videoDevice == null)
                    {
                        //todo notify unsupported!
                        State = CameraProcessorState.Error;
                        session.CommitConfiguration();
                        return;
                    }
                }
            }

            //Get a list of supported formats for the device
            var allFormats =
                videoDevice.Formats.ToList(); //.Select(x => x.HighResolutionStillImageDimensions).ToList();

            AVCaptureDeviceFormat format = null;
            if (UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
            {
                format = allFormats.FirstOrDefault(x =>
                    x.HighResolutionStillImageDimensions.Width ==
                    allFormats.Max(m => m.HighResolutionStillImageDimensions.Width) && x.MultiCamSupported);
            }

            if (format == null)
            {
                //no multicam   
#if DEBUG
                foreach (var f in allFormats)
                {
                    //fov {f.VideoFieldOfView}, valid {f.HighestPhotoQualitySupported},
                    Debug.WriteLine(
                        $"Format  {f.HighResolutionStillImageDimensions.Width}x{f.HighResolutionStillImageDimensions.Height}");
                    Debug.WriteLine($"Format  fov {f.VideoFieldOfView}");
                }
#endif
                format = allFormats.FirstOrDefault(x =>
                    x.HighResolutionStillImageDimensions.Width ==
                    allFormats.Max(m => m.HighResolutionStillImageDimensions.Width));
            }

            // Cap the video framerate at the max depth framerate.
            CMTime frameDuration = CMTime.Zero;
            var frameDurationRange = videoDevice.ActiveDepthDataFormat?.VideoSupportedFrameRateRanges.First();
            if (frameDurationRange != null)
            {
                frameDuration = frameDurationRange.MinFrameDuration;
            }

            videoDevice.LockForConfiguration(out NSError lerror);
            if (lerror == null)
            {
                videoDevice.SmoothAutoFocusEnabled = videoDevice.SmoothAutoFocusSupported;
                videoDevice.ActiveFormat = format;

                //if (frameDuration != CMTime.Zero)
                //{
                //	videoDevice.ActiveVideoMinFrameDuration = frameDuration;
                //}

                //videoDevice.ActiveVideoMinFrameDuration = new CMTime(1, 60);
                //videoDevice.ActiveVideoMaxFrameDuration = new CMTime(1, 60);

                videoDevice.UnlockForConfiguration();
            }


            // Add a video input

            //clear first
            //if (_deviceInput != null)
            //{
            //    session.RemoveInput(_deviceInput);
            //}

            var flipCamera = _deviceInput != null;

            if (flipCamera)
                session.RemoveInput(_deviceInput);

            while (session.Inputs.Any())
            {
                session.RemoveInput(session.Inputs[0]);
            }

            // Select a video device, make an input
            //var videoDevice = AVCaptureDeviceDiscoverySession.Create(
            //    new AVCaptureDeviceType[] { AVCaptureDeviceType.BuiltInWideAngleCamera },
            //    AVMediaType.Video,
            //    AVCaptureDevicePosition.Back
            //).Devices.FirstOrDefault();

            var deviceInput = new AVCaptureDeviceInput(videoDevice, out NSError error);
            if (error != null)
            {
                Console.WriteLine($"Could not create video device input: {error.LocalizedDescription}");
                session.CommitConfiguration();
                State = CameraProcessorState.Error;
                return;
            }

            Input = deviceInput;

            //if (!session.CanAddInput(_deviceInput))
            //{
            //    Console.WriteLine($"[CAMERA] Could not add '{cameraPosition}' device input to the session");
            //    session.CommitConfiguration();
            //    return;
            //}
            try
            {
                session.AddInput(_deviceInput);
            }
            catch (Exception e)
            {
                Console.WriteLine($"[CAMERA] Could not add '{cameraPosition}' device input to the session: {e}");
                session.CommitConfiguration();
                State = CameraProcessorState.Error;
                return;
            }


            //Debug.WriteLine($"[CameraFragment] Cameras:\n {ViewFinderData.PrettyJson(BackCameras)}");


            var dictionary = new NSMutableDictionary();
            dictionary[AVVideo.CodecKey] = new NSNumber((int)AVVideoCodec.JPEG);
            stillImageOutput = new AVCaptureStillImageOutput() { OutputSettings = new NSDictionary() };
            stillImageOutput.HighResolutionStillImageOutputEnabled = true;
            session.AddOutput(stillImageOutput);
            //todo add delegate for sound remove

            Debug.WriteLine($"[CAMERA] {cameraPosition} enabled");


            if (session.CanAddOutput(videoDataOutput))
            {
                session.AddOutput(videoDataOutput);
                // Add a video data ouptut
                videoDataOutput.AlwaysDiscardsLateVideoFrames = true;

                //                    videoDataOutput.AutomaticallyConfiguresOutputBufferDimensions = true;
                videoDataOutput.WeakVideoSettings = new NSDictionary(CVPixelBuffer.PixelFormatTypeKey,
                    pixelFormat);
                videoDataOutput.SetSampleBufferDelegate(CameraCapture, CameraCapture.VideoDataOutputQueue);
                //videoDataOutput.SetSampleBufferDelegateQueue(CameraCapture, CameraCapture.VideoDataOutputQueue);
            }
            else
            {
                Console.WriteLine("Could not add video data output to the session");
                session.CommitConfiguration();
                State = CameraProcessorState.Error;
                return;
            }

            // Add a depth data output

            if (session.CanAddOutput(depthDataOutput))
            {
                session.AddOutput(depthDataOutput);
                depthDataOutput.SetDelegate(CameraCapture, CameraCapture.VideoDataOutputQueue);
                depthDataOutput.FilteringEnabled = false;

                var depthConnection = depthDataOutput.ConnectionFromMediaType(AVMediaTypes.DepthData.GetConstant());
                var check = depthDataOutput.Connections;

                if (depthConnection != null)
                {
                    depthConnection.Enabled = true;
                }
                else
                {
                    Console.WriteLine("Could not add depth data output to the session as unavalable");
                }
            }
            else
            {
                Console.WriteLine("Could not add depth data output to the session");
                session.CommitConfiguration();
                return;
            }


            var captureConnection = videoDataOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());

            /*
            if (UIDevice.CurrentDevice.CheckSystemVersion(11, 0))
            {
                if (captureConnection.CameraIntrinsicMatrixDeliverySupported)
                {
                    captureConnection.CameraIntrinsicMatrixDeliveryEnabled = true;
                }
            }
            */

            // Always process the frames
            captureConnection.Enabled = true;
            videoDevice.LockForConfiguration(out NSError error2);
            if (error2 == null)
            {
                var formatDescription = videoDevice.ActiveFormat.FormatDescription as CMVideoFormatDescription;
                CMVideoDimensions dimensions = formatDescription.Dimensions;
                bufferSize.Width = dimensions.Width;
                bufferSize.Height = dimensions.Height;
                videoDevice.UnlockForConfiguration();
            }
            else
            {
                Console.WriteLine($"{error2.LocalizedDescription}");
            }


            session.CommitConfiguration();

            if (videoDataOutput.Connections.Any())
            {
                var maybe = videoDataOutput.Connections[0].VideoOrientation;
                _videoOrientation = maybe;
                UpdateDetectOrientation();
            }

            // start the capture
            session.StartRunning();

            if (_options.NeedCalibration && _options.CalibratedDevice == null)
            {
                Task.Run(TakeCalibrationPhoto).ConfigureAwait(false);
            }

            this.Camera = _options.CalibratedDevice;

            _hardwareOk = true;
        }

        #endregion

        protected UIColor GetColorAt(UIImage image, int x, int y)
        {
            //Marshal.UnsafeAddrOfPinnedArrayElement(rawData, 0)
            var rawData = new byte[4];
            var handle = GCHandle.Alloc(rawData);
            UIColor resultColor = null;
            try
            {
                //using (var colorSpace = CGColorSpace.CreateDeviceRGB())
                //{
                //    using (var context = new CGBitmapContext(rawData, 1, 1, 8, 4, colorSpace,
                //        CGImageAlphaInfo.PremultipliedLast))
                //    {
                //        context.DrawImage(new CGRect(-x, y - image.Size.Height, image.Size.Width, image.Size.Height),
                //            image.CGImage);
                //        float red = (rawData[0]) / 255.0f;
                //        float green = (rawData[1]) / 255.0f;
                //        float blue = (rawData[2]) / 255.0f;
                //        float alpha = (rawData[3]) / 255.0f;
                //        resultColor = UIColor.FromRGBA(red, green, blue, alpha);

                //        Debug.WriteLine($"Got color {resultColor} at {x},{y} in image {image.Size.Width}x{image.Size.Height}");

                //        OutColor = new nfloat[] { 0f, 0f, 0f, 0f };
                //        OutColor[0] = red;
                //        OutColor[1] = green;
                //        OutColor[2] = blue;
                //        OutColor[3] = alpha;
                //    }
                //}

                using (var colorSpace = CGColorSpace.CreateGenericRgb())
                {
                    using (var context = new CGBitmapContext(rawData, 1, 1, 8, 4, colorSpace,
                               CGImageAlphaInfo.PremultipliedLast))
                    {
                        context.DrawImage(
                            new RectangleF(-x, y - (float)image.Size.Height, (float)image.Size.Width,
                                (float)image.Size.Height), image.CGImage);


                        float red = (rawData[0]) / 255.0f;
                        float green = (rawData[1]) / 255.0f;
                        float blue = (rawData[2]) / 255.0f;
                        float alpha = (rawData[3]) / 255.0f;
                        resultColor = UIColor.FromRGBA(red, green, blue, alpha);

                        Debug.WriteLine(
                            $"Got color {resultColor} at {x},{y} in image {image.Size.Width}x{image.Size.Height}");

                        BlackColor = new nfloat[] { 0f, 0f, 0f, 0f };
                        BlackColor[0] = red;
                        BlackColor[1] = green;
                        BlackColor[2] = blue;
                        BlackColor[3] = alpha;

                        //var hexCode = string.Format("#{0}{1}{2}{3}", rawData[3].ToString("X2"),
                        //    rawData[0].ToString("X2"), rawData[1].ToString("X2"), rawData[2].ToString("X2"));
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e.ToString());
            }
            finally
            {
                handle.Free();
            }

            return resultColor;
        }

        protected UIColor GetColorAt(IMTLTexture texture, UIImageOrientation orientation, int x, int y)
        {
            using var cImg = new CIImage(texture, null); //     var image = CIImage.FromData(imageData); 
            using var
                cgImg = _context.CreateCGImage(cImg,
                    cImg.Extent); //     CGImage cgImage = context.CreateCGImage(image, image.Extent); 

            var uiImg = UIImage.FromImage(cgImg, 1f, orientation);

            var imageScaleY = (nfloat)(uiImg.Size.Height) / this.Bounds.Height;
            var imageScaleX = (nfloat)(uiImg.Size.Width) / this.Bounds.Width;

            var getX = (int)(x * imageScaleX);

            var getY = (int)(uiImg.Size.Height - y * imageScaleY);


            Debug.WriteLine($"Tapped at {x},{y} scale {imageScaleX} {imageScaleY}");

            return GetColorAt(uiImg, getX, getY);
        }

        #region Output Metal

        Semaphore metalSemaphore;

        public void UpdateDetectOrientation()
        {
            if (videoDataOutput.Connections.Any())
            {
                Rotation = GetRotation(
                    _uiOrientation,
                    _videoOrientation,
                    this._deviceInput.Device.Position);

                switch (_uiOrientation)
                {
                    case UIInterfaceOrientation.Portrait:
                        _orientation = UIImageOrientation.Right;
                        break;
                    case UIInterfaceOrientation.PortraitUpsideDown:
                        _orientation = UIImageOrientation.Left;
                        break;
                    case UIInterfaceOrientation.LandscapeLeft:
                        _orientation = UIImageOrientation.Up;
                        break;
                    case UIInterfaceOrientation.LandscapeRight:
                        _orientation = UIImageOrientation.Down;
                        break;
                    default:
                        _orientation = UIImageOrientation.Up;
                        break;
                }

                try
                {
                    FrameWidth = (nint)Frame.Size.Width;
                    FrameHeight = (nint)Frame.Size.Height;
                    IsPortrait = FrameHeight > FrameWidth;
                }
                catch (Exception e)
                {
                    App.Logger.Error(this.GetType().Name, e);
                }

                Debug.WriteLine(
                    $"[UpdateDetectOrientation]: rotation: {Rotation}, orientation: {_orientation}, \r\n device: {_deviceInput.Device.Position}, video: {_videoOrientation}, ui:{_uiOrientation}");
            }
        }

        //For acces on non ui thread:
        public nint FrameWidth { get; set; }
        public nint FrameHeight { get; set; }
        protected int debug = 0;

        #endregion

        protected bool internalMirroring;
        protected Rotation internalRotation;
        protected CVPixelBuffer internalPixelBuffer;
        protected bool _mirroring;

        public bool mirroring
        {
            get { return _mirroring; }
            set
            {
                if (_mirroring != value)
                {
                    _mirroring = value;
                    internalMirroring = mirroring;
                }
            }
        }

        protected Rotation _rotation;

        public Rotation Rotation
        {
            get { return _rotation; }
            set
            {
                if (_rotation != value)
                {
                    _rotation = value;
                    internalRotation = value;
                }
            }
        }

        protected CVPixelBuffer _pixelBuffer;

        public CVPixelBuffer pixelBuffer
        {
            get { return _pixelBuffer; }
            set
            {
                if (_pixelBuffer != value)
                {
                    _pixelBuffer = value;
                    internalPixelBuffer = pixelBuffer;
                }
            }
        }

        #region AVCamera

        protected CGRect internalBounds = new CGRect();

        //protected nuint textureWidth = 0;
        //protected nuint textureHeight = 0;
        protected bool textureMirroring = false;
        protected StretchModes textureScretchMode;
        protected Rotation textureRotation = Rotation.rotate0Degrees;

        //protected CGAffineTransform textureTranform;
        protected NSMutableDictionary _metaData;
        protected UIImageOrientation _orientation;
        protected AVCaptureVideoOrientation _videoOrientation;
        protected UIInterfaceOrientation _uiOrientation;
        public StretchModes DisplayMode { get; set; } = StretchModes.Fill;

        #endregion

        #region TEXTURE

        private float _ViewportScale = 1.0f;

        public float ViewportScale
        {
            get { return _ViewportScale; }
            set
            {
                if (_ViewportScale != value && !Single.IsNaN(value))
                {
                    _ViewportScale = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _PreviewWidth;

        public int PreviewWidth
        {
            get { return _PreviewWidth; }
            set
            {
                if (_PreviewWidth != value && !Single.IsNaN(value))
                {
                    _PreviewWidth = value;
                    OnPropertyChanged("PreviewWidth");
                }
            }
        }

        private int _PreviewHeight;

        public int PreviewHeight
        {
            get { return _PreviewHeight; }
            set
            {
                if (_PreviewHeight != value && !Single.IsNaN(value))
                {
                    _PreviewHeight = value;
                    OnPropertyChanged("PreviewHeight");
                }
            }
        }

        private double _lastTextureZoom;
        public float AdjustTexture = 1.0f;
        private bool lockTransfrom;

        /// </summary>
        /// <param name="width"></param>
        /// <param name="height"></param>
        /// <param name="mirroring"></param>
        /// <param name="rotation"></param>
        void SetupTransform(nuint width, nuint height, bool mirroring, Rotation rotation, double zoomScale = 1.0,
            StretchModes displayMode = StretchModes.Fill)
        {
            if (lockTransfrom)
                return;

            lockTransfrom = true;

            _lastTextureZoom = zoomScale;

            textureMirroring = mirroring;
            textureRotation = rotation;

            internalBounds = new CGRect(this.Bounds.X, Bounds.Y, Bounds.Width, Bounds.Height);

            Debug.WriteLine($"[SetupTransform] rotation:{textureRotation} width:{PreviewWidth}");

            float scaleX = 1.0f;
            float scaleY = 1.0f;

            var textureWidth = width;
            var textureHeight = height;

            var viewWidth = internalBounds.Width;
            var viewHeight = internalBounds.Height;


            if (textureRotation == Rotation.rotate90Degrees || textureRotation == Rotation.rotate270Degrees)
            {
                textureWidth = height;
                textureHeight = width;
            }

            PreviewHeight = (int)textureHeight;
            PreviewWidth = (int)textureWidth;


            var aspectTexture = (float)textureHeight / (float)textureWidth;

            var viewRatioX = (float)(viewWidth / (float)textureWidth);
            var viewRatioY = (float)(viewHeight / (float)textureHeight);

            var viewRatio = Math.Max(viewRatioX, viewRatioY);
            //var resultHeight = textureHeight * viewRatio;
            //var resultWidth = textureWidth * viewRatio;

            var scaleWidth = (float)Bounds.Width / (float)textureWidth;
            var scaleHeight = (float)Bounds.Height / (float)textureHeight;


            if (displayMode == StretchModes.Fit) //ASPECT FIT
            {
                //MAX size is 1.0, min varies..
                var diff = 1 - (Math.Max(scaleWidth, scaleHeight) - Math.Min(scaleWidth, scaleHeight));

                if (scaleWidth > scaleHeight)
                {
                    scaleY = 1.0f; //fits
                    scaleX = scaleHeight / scaleWidth; //reduced
                }
                else
                {
                    scaleX = 1.0f; //fitd
                    scaleY = scaleWidth / scaleHeight; //reduced
                }
            }
            else if (displayMode == StretchModes.Fill) //ASPECT FILL
            {
                //min size is 1.0, max varies..
                var diff = 1 + Math.Max(scaleWidth, scaleHeight) - Math.Min(scaleWidth, scaleHeight);

                if (scaleWidth > scaleHeight)
                {
                    scaleX = 1.0f; //fills
                    scaleY = scaleWidth / scaleHeight; //enlarged
                }
                else
                {
                    scaleY = 1.0f; //fills
                    scaleX = scaleHeight / scaleWidth; //enlarged
                }
            }


            //how much texture was zoomed in viewport
            ViewportScale = scaleX * scaleY;


            if (textureMirroring)
            {
                scaleX *= -1.0f;
            }

            //*** apply manual zoom ***
            scaleX *= (float)zoomScale;
            scaleY *= (float)zoomScale;

            // Vertex coordinate takes the gravity into account.
            float[] vertexData =
            {
                -scaleX, -scaleY, 0.0f, 1.0f, scaleX, -scaleY, 0.0f, 1.0f, -scaleX, scaleY, 0.0f, 1.0f, scaleX,
                scaleY, 0.0f, 1.0f
            };

            vertexCoordBuffer = Device.CreateBuffer(vertexData, new MTLResourceOptions());

            // Texture coordinate takes the rotation into account.
            float[] textData = new[] { 0.0f, 1.0f, 1.0f, 1.0f, 0.0f, 0.0f, 1.0f, 0.0f };

            switch (textureRotation)
            {
                case Rotation.rotate0Degrees:
                    textData = new[] { 0.0f, 1.0f, 1.0f, 1.0f, 0.0f, 0.0f, 1.0f, 0.0f };
                    break;

                case Rotation.rotate180Degrees:
                    textData = new[] { 1.0f, 0.0f, 0.0f, 0.0f, 1.0f, 1.0f, 0.0f, 1.0f };
                    break;

                case Rotation.rotate90Degrees:
                    textData = new[] { 1.0f, 1.0f, 1.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f };
                    break;

                case Rotation.rotate270Degrees:
                    textData = new[] { 0.0f, 0.0f, 0.0f, 1.0f, 1.0f, 0.0f, 1.0f, 1.0f };
                    break;
            }

            textureCoordBuffer = Device.CreateBuffer(textData, new MTLResourceOptions());

            lockTransfrom = false;
        }

        #endregion

        public Rotation GetRotation(
            UIInterfaceOrientation interfaceOrientation,
            AVCaptureVideoOrientation videoOrientation,
            AVCaptureDevicePosition cameraPosition)
        {
            /*
             Calculate the rotation between the videoOrientation and the interfaceOrientation.
             The direction of the rotation depends upon the camera position.
             */

            switch (videoOrientation)
            {
                case AVCaptureVideoOrientation.Portrait:
                    switch (interfaceOrientation)
                    {
                        case UIInterfaceOrientation.LandscapeRight:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate90Degrees;
                            }
                            else
                            {
                                return Rotation.rotate270Degrees;
                            }

                        case UIInterfaceOrientation.LandscapeLeft:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate270Degrees;
                            }
                            else
                            {
                                return Rotation.rotate90Degrees;
                            }

                        case UIInterfaceOrientation.Portrait:
                            return Rotation.rotate0Degrees;

                        case UIInterfaceOrientation.PortraitUpsideDown:
                            return Rotation.rotate180Degrees;

                        default:
                            return Rotation.rotate0Degrees;
                    }

                case AVCaptureVideoOrientation.PortraitUpsideDown:
                    switch (interfaceOrientation)
                    {
                        case UIInterfaceOrientation.LandscapeRight:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate270Degrees;
                            }
                            else
                            {
                                return Rotation.rotate90Degrees;
                            }

                        case UIInterfaceOrientation.LandscapeLeft:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate90Degrees;
                            }
                            else
                            {
                                return Rotation.rotate270Degrees;
                            }

                        case UIInterfaceOrientation.Portrait:
                            return Rotation.rotate180Degrees;


                        case UIInterfaceOrientation.PortraitUpsideDown:
                            return Rotation.rotate0Degrees;

                        default:
                            return Rotation.rotate0Degrees;
                    }

                case AVCaptureVideoOrientation.LandscapeRight:


                    switch (interfaceOrientation)
                    {
                        case UIInterfaceOrientation.LandscapeRight:
                            return Rotation.rotate0Degrees;

                        case UIInterfaceOrientation.LandscapeLeft:
                            return Rotation.rotate180Degrees;

                        case UIInterfaceOrientation.Portrait:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate270Degrees;
                            }
                            else
                            {
                                return Rotation.rotate90Degrees;
                            }

                        case UIInterfaceOrientation.PortraitUpsideDown:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate90Degrees;
                            }
                            else
                            {
                                return Rotation.rotate270Degrees;
                            }

                        default:
                            return Rotation.rotate0Degrees;
                    }

                case AVCaptureVideoOrientation.LandscapeLeft:
                    switch (interfaceOrientation)
                    {
                        case UIInterfaceOrientation.LandscapeLeft:
                            return Rotation.rotate0Degrees;

                        case UIInterfaceOrientation.LandscapeRight:
                            return Rotation.rotate180Degrees;

                        case UIInterfaceOrientation.Portrait:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate90Degrees;
                            }
                            else
                            {
                                return Rotation.rotate270Degrees;
                            }

                        case UIInterfaceOrientation.PortraitUpsideDown:
                            if (cameraPosition == AVCaptureDevicePosition.Front)
                            {
                                return Rotation.rotate270Degrees;
                            }
                            else
                            {
                                return Rotation.rotate90Degrees;
                            }

                        default:
                            return Rotation.rotate0Degrees;
                    }

                //@unknown default:
                //fatalError("Unknown orientation.")
            }

            return Rotation.rotate0Degrees;
        }

        UIImage ImageFromSampleBuffer(CMSampleBuffer sampleBuffer)
        {
            // Get the CoreVideo image
            using (var pixelBuffer = sampleBuffer.GetImageBuffer() as CVPixelBuffer)
            {
                // Lock the base address
                pixelBuffer.Lock(CVPixelBufferLock.None);
                // Get the number of bytes per row for the pixel buffer
                var baseAddress = pixelBuffer.BaseAddress;
                int bytesPerRow = (int)pixelBuffer.BytesPerRow;
                int width = (int)pixelBuffer.Width;
                int height = (int)pixelBuffer.Height;
                var flags = CGBitmapFlags.PremultipliedFirst | CGBitmapFlags.ByteOrder32Little;
                // Create a CGImage on the RGB colorspace from the configured parameter above
                using (var cs = CGColorSpace.CreateDeviceRGB())
                using (var context = new CGBitmapContext(baseAddress, width, height, 8, bytesPerRow, cs,
                           (CGImageAlphaInfo)flags))
                using (var cgImage = context.ToImage())
                {
                    pixelBuffer.Unlock(CVPixelBufferLock.None);
                    return UIImage.FromImage(cgImage);
                }
            }
        }

        [Export("captureOutput:didDrop:fromConnection:")]
        public virtual void DidDrop(
            AVCaptureOutput captureOutput,
            CMSampleBuffer sampleBuffer,
            AVCaptureConnection connection)
        {
            //textureCache.Flush(CVOptionFlags.None);
        }

        private bool _IsBusy;

        public bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                }
            }
        }

        public Action<UIImage> CapturedImage;
        PHAssetCollection _album;

        /// <summary>
        /// Get or create album
        /// </summary>
        /// <param name="title"></param>
        /// <param name="continueWith"></param>
        /// <param name="onError"></param>
        void CreateAlbum(string title, Action continueWith, Action onError)
        {
            void LoadAlbum()
            {
                var fetchOptions = new PHFetchOptions { Predicate = NSPredicate.FromFormat($"title = \"{title}\"") };
                var collections = PHAssetCollection.FetchAssetCollections(PHAssetCollectionType.Album,
                    PHAssetCollectionSubtype.Any, fetchOptions);
                _album = collections.firstObject as PHAssetCollection;
            }

            LoadAlbum();
            if (_album == null)
            {
                PHPhotoLibrary.SharedPhotoLibrary.PerformChanges(
                    () => { PHAssetCollectionChangeRequest.CreateAssetCollection(title); }, (issuccess, error) =>
                    {
                        if (issuccess)
                        {
                            LoadAlbum();
                            continueWith?.Invoke();
                        }
                        else
                        {
                            Console.WriteLine(error);
                            onError?.Invoke();
                        }
                    });
            }
            else
            {
                continueWith?.Invoke();
            }
        }

        /// <summary>
        /// Actual method for taking hi res photo
        /// </summary>
        public async Task<bool> TakePhoto()
        {
            try
            {
                if (IsBusy)
                    return false;

                IsBusy = true;

                var canSave = PHPhotoLibrary.AuthorizationStatus;
                if (canSave != PHAuthorizationStatus.Authorized)
                {
                    InvokeOnMainThread(async () =>
                    {
                        var status = await PHPhotoLibrary.RequestAuthorizationAsync();
                        if (status != PHAuthorizationStatus.Authorized)
                        {
                            Toast.ShortMessage(ResStrings.NoPermissions);
                        }
                    });
                    NativePhotoTakingFailed.Invoke(this, null);
                    return false;
                }


                //_metalTakePicture = false;

                InvokeOnMainThread(() => { FlashScreen(); });

                var videoConnection = stillImageOutput.ConnectionFromMediaType(AVMediaTypes.Video.GetConstant());
                var sampleBuffer = await stillImageOutput.CaptureStillImageTaskAsync(videoConnection);
                var jpegImage = AVCaptureStillImageOutput.JpegStillToNSData(sampleBuffer);

                //exif part
                var image = CIImage.FromData(jpegImage);

                var h = image.Properties.PixelHeight;
                var w = image.Properties.PixelWidth;

                var ww = image.Extent.Width;
                var hh = image.Extent.Height;

                var sensor = SensorRotation;

                _metaData = image.Properties.Dictionary.MutableCopy() as NSMutableDictionary;
                var o = _metaData["Orientation"];

                _props = image.Properties;

#if DEBUG
                var exif = image.Properties.Exif;
                foreach (var key in exif.Dictionary.Keys)
                {
                    Debug.WriteLine($"{key}: {exif.Dictionary[key]}");
                }
#endif

                try
                {
                    var texture = new Texture(jpegImage);
                    if (texture == null)
                    {
                        IsBusy = false;
                        NativePhotoTakingFailed.Invoke(this, null);
                        return false;
                    }

                    var ok = texture.Finalize(Device, (text, error) =>
                    {
                        try
                        {
                            //if (OfflineInTexture != null)
                            //{
                            //    OfflineInTexture.Dispose();
                            //}
                            OfflineInTexture = texture.MetalTexture.CreateTextureView(MTLPixelFormat.BGRA8Unorm);
                            if (OfflineInTexture != null)
                            {
                                CaptureWidth = (int)OfflineInTexture.Width;
                                CaptureHeight = (int)OfflineInTexture.Height;

                                var computedTexture = ProcessTextureOffline(OfflineInTexture);
                                if (computedTexture != null)
                                {
                                    //save
                                    var orientation = GetCurrentOrientation();

                                    //exif is added inside
                                    NSData jpeg = ConvertTextureToJpeg(computedTexture, orientation,
                                        //addExif,
                                        CGColorSpace.CreateGenericRgbLinear());

                                    if (CaptureLocation == CaptureLocationType.Bitmap)
                                    {
                                        SavedRotation = sensor;
                                        var image = UIImage.LoadFromData(jpeg);
                                        CapturedImage?.Invoke(image);
                                        return;
                                    }

                                    //consider it to have exif already in jpeg
                                    SavePicture(jpeg, sensor);

#if DEBUG
                                    //  SaveJpegToGallery(jpeg, GenerateJpgFileName());
#endif


                                    Debug.WriteLine($"Texture saved!");
                                    NativePhotoTaken?.Invoke(this, null);
                                }

                                NativePhotoTaken?.Invoke(this,
                                    EventArgs.Empty); //we gonna process texture and save to file later
                            }
                            else
                            {
                                throw new Exception("CreateTextureView returned NULL");
                            }
                        }
                        catch (Exception ex)
                        {
                            NativePhotoTakingFailed.Invoke(this, null);
                            Console.WriteLine(ex);
                        }
                        finally
                        {
                            IsBusy = false;
                        }
                    });

                    if (!ok)
                    {
                        NativePhotoTakingFailed.Invoke(this, null);
                        IsBusy = false;
                        return false;
                    }


                    //NativePhotoTaken?.Invoke(this, EventArgs.Empty);
                }
                catch (Exception x)
                {
                    Console.WriteLine(x.ToString());
                    NativePhotoTakingFailed.Invoke(this, null);
                    IsBusy = false;
                    return false;
                }
                finally
                {
                    //sampleBuffer?.Dispose();
                }


                //var jpegImage = AVCaptureStillImageOutput.JpegStillToNSData(sampleBuffer);

                //var photo = new UIImage(jpegImage);
                //photo.SaveToPhotosAlbum((image, error) =>
                //{
                //    if (!string.IsNullOrEmpty(error?.LocalizedDescription))
                //    {
                //        Console.Error.WriteLine($"\t\t\tError: {error.LocalizedDescription}");
                //    }
                //});

                //   var jpegAsByteArray = jpegImageAsNsData.ToArray();

                return true;
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);
                IsBusy = false;
                return false;
            }
            finally
            {
            }

            // TODO: Send this to local storage or cloud storage such as Azure Storage.
        }

        private int _FPS;

        public int FPS
        {
            get { return _FPS; }
            set
            {
                if (_FPS != value)
                {
                    _FPS = value;
                }
            }
        }

        private System.Timers.Timer _timer;
        protected int countFrames = 0;

        private void OnTimerEverySecond(object sender, ElapsedEventArgs e)
        {
            FPS = countFrames;
            countFrames = 0;
            OnUpdateFPS?.Invoke(null, new EventArgs());
        }

        public event EventHandler OnUpdateFPS;
        private TexturesQueue texturesQueue = new TexturesQueue();

        protected int LockDraw
        {
            get { return _lockDraw; }
            set
            {
                if (value < 0)
                {
                    _lockDraw = 0;
                }
                else
                {
                    _lockDraw = value;
                }
                //Console.WriteLine($"LOCK {_lockDraw}");
            }
        }

        static int maxInflightBuffers = 1;
        int _lastHeight = -1;
        int _lastWidth = -1;

        public override void Draw()
        {
            if (CurrentRenderPassDescriptor == null || State != CameraProcessorState.Enabled ||
                CameraCapture.sourceTexture == null || CommandQueue == null) //
            {
                base.Draw();
                return;
            }


            metalSemaphore.WaitOne();

            //

            ICAMetalDrawable drawable = null;


            try
            {
                // ???? Create a new command buffer for each renderpass to the current drawable.

                bool mirroring = false;
                Rotation thisRotation = Rotation.rotate0Degrees;

                mirroring = internalMirroring;
                thisRotation = internalRotation;

                var inTexture = CameraCapture.sourceTexture;

                if (inTexture.Width != (nuint)_lastWidth ||
                    inTexture.Height != (nuint)_lastHeight ||
                    this.Bounds != internalBounds ||
                    mirroring != textureMirroring ||
                    (_manualZoomTexture != nfloat.NaN && _lastTextureZoom != _manualZoomTexture) ||
                    textureScretchMode != DisplayMode ||
                    thisRotation != textureRotation)
                {
                    _lastTextureZoom = _manualZoomTexture;
                    _lastHeight = (int)inTexture.Height;
                    _lastWidth = (int)inTexture.Width;

                    textureScretchMode = DisplayMode;
                    SetupTransform(inTexture.Width, inTexture.Height, mirroring, thisRotation, _lastTextureZoom,
                        DisplayMode);
                }

                //CommandQueue = Device.CreateCommandQueue(maxInflightBuffers);
                //if (CommandQueue == null)
                //    return;

                IMTLCommandBuffer buffer = CommandQueue.CommandBuffer();
                IMTLTexture texture = null;

                //Set background color of the rendered output 
                //var atts = this.CurrentRenderPassDescriptor.ColorAttachments;
                //if (atts != null)
                //{
                //    atts[0].ClearColor = new MTLClearColor(0, 1, 0, 1);
                //    atts[0].LoadAction = MTLLoadAction.Clear;
                //}

                //COMPUTE
                var kernel = Filters.GetFilter(_currentCameraEffect);
                var computedTexture = buffer.CreateTextureWith(inTexture);
                if (kernel != null)
                {
                    var computeEncoder = buffer.ComputeCommandEncoder;

                    if (computeEncoder == null)
                    {
                        return;
                    }

                    computeEncoder.SetComputePipelineState(kernel.Compiled);
                    computeEncoder.SetTexture(inTexture, index: 0);
                    computeEncoder.SetTexture(computedTexture, index: 1);

                    if (!kernel.Initialized)
                    {
                        return;
                    }

                    if (kernel.HasLUT)
                    {
                        kernel.AttachLUT(ref computeEncoder); //in 0
                        computeEncoder.SetBuffer(_previewOptions, 0, 1);
                    }
                    else if (kernel.HasArguments)
                    {
                        kernel.AttachArguments(ref computeEncoder); //in 0 1 2
                        computeEncoder.SetBuffer(_previewOptions, 0, 3);
                    }
                    else
                    {
                        computeEncoder.SetBuffer(_previewOptions, 0, 3);
                    }


                    //if (kernel.Arguments != null)
                    //{
                    //    if (_lastTitle != kernel.Arguments.Tag)
                    //    {
                    //        _lastTitle = kernel.Arguments.Tag;
                    //        Debug.WriteLine($"Buffers changed!.. {_lastTitle}");
                    //    }
                    //    computeEncoder.SetBuffer(kernel.Arguments.Red, 0, 0);
                    //    computeEncoder.SetBuffer(kernel.Arguments.Green, 0, 1);
                    //    computeEncoder.SetBuffer(kernel.Arguments.Blue, 0, 2);
                    //}

                    kernel.DispatchThreadgroups(ref computeEncoder, inTexture.Width, inTexture.Height);

                    computeEncoder.EndEncoding();

                    texture = computedTexture;
                }
                //END OF COMPUTE

                else //no kernel used
                {
                    texture = inTexture;
                }

                //RENDER
                IMTLRenderCommandEncoder renderEncoder = buffer.CreateRenderCommandEncoder(CurrentRenderPassDescriptor);

                drawable = this.CurrentDrawable;
                //drawable = ((CAMetalLayer)Layer).NextDrawable();

                //Encode(renderEncoder);

                renderEncoder.Label = "Preview display";
                renderEncoder.SetRenderPipelineState(pipelineState);

                renderEncoder.SetVertexBuffer(vertexCoordBuffer, offset: 0, index: 0);
                renderEncoder.SetVertexBuffer(textureCoordBuffer, offset: 0, index: 1);

                //input
                renderEncoder.SetFragmentTexture(texture, index: 0);

                renderEncoder.SetFragmentSamplerState(sampler, index: 0);
                renderEncoder.DrawPrimitives(MTLPrimitiveType.TriangleStrip, 0, 4);

                renderEncoder.EndEncoding();
                //END OF RENDER


                //var texture = drawable.Texture;

                buffer.AddCompletedHandler((IMTLCommandBuffer buff) =>
                {
                    if (TexturePoint != null &&
                        (pickerMode == CameraPickerMode.NegativeBlack || pickerMode == CameraPickerMode.NegativeWhite))
                    {
                        //    pickerMode = CameraPickerMode.None;

                        try
                        {
                            var target = drawable.Texture;

                            var textureRect = new CGRect(TexturePoint.Value, size: CGSize.Empty);
                            TappedOnColor = GetColorAt(target, GetCurrentOrientation(), (int)textureRect.X,
                                (int)textureRect.Y);
                            OnBlackColorChanged?.Invoke(this, EventArgs.Empty);

                            //EncodeBlackColor();
                        }
                        catch (Exception e)
                        {
                            App.Logger.Error(this.GetType().Name, e);
                        }

                        TexturePoint = null;
                    }

                    base.Draw();

                    computedTexture?.Dispose();
                    computedTexture = null;
                    inTexture = null;

                    texture = null;
                    drawable?.Dispose();

                    countFrames++;

                    metalSemaphore.Release();
                });

                buffer.PresentDrawable(drawable);
                buffer.Commit();
            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);
                textureCache.Flush(CVOptionFlags.None);
                drawable?.Dispose();
                metalSemaphore.Release();
            }
            finally
            {
            }
        }

        public event EventHandler OnBlackColorChanged;
        public event EventHandler NativePhotoTaken;
        public event EventHandler NativePhotoTakingFailed;

        public void ProcessColorUnderTouch(CGPoint point, CameraPickerMode mode)
        {
            TexturePoint = point;
            pickerMode = mode;
        }

        protected CameraPickerMode pickerMode = CameraPickerMode.None;
        private string _lastTitle;
        private int _lockDraw;
        public UIColor TappedOnColor { get; set; }
        public bool IsPortrait { get; set; }
        protected CVPixelBuffer CapturedBuffer { get; set; }
        public CGPoint? TexturePoint { get; set; }
        public CameraCapture CameraCapture { get; protected set; }

        #region TAKE PHOTO

        public string SavedFilename
        {
            get { return _SavedFilename; }
            set
            {
                if (_SavedFilename != value)
                {
                    _SavedFilename = value;
                    OnPropertyChanged("SavedFilename");
                }
            }
        }

        private string _SavedFilename;
        private double _SavedRotation;

        public double SavedRotation
        {
            get { return _SavedRotation; }
            set
            {
                if (_SavedRotation != value)
                {
                    _SavedRotation = value;
                    OnPropertyChanged("SavedRotation");
                }
            }
        }

        public string CaptureCustomFolder { get; set; }
        public CaptureLocationType CaptureLocation { get; set; }
        private static int filenamesCounter = 0;
        private readonly CameraPreviewRenderer _renderer;

        public string GenerateJpgFileName()
        {
            var add = $"{DateTime.Now:MM/dd/yyyy HH:mm:ss}{filenamesCounter}";
            var filename =
                $"artoffoto-{add.Replace("/", "").Replace(":", "").Replace(" ", "").Replace(",", "").Replace(".", "").Replace("-", "")}.jpg";

            filenamesCounter++;

            return filename;
        }

        public double SensorRotation
        {
            get
            {
                try
                {
                    var orientation = (DeviceOrientation)(int)UIDevice.CurrentDevice.Orientation;

                    if (orientation == DeviceOrientation.PortraitUpsideDown)
                        return 180.0;

                    if (orientation == DeviceOrientation.LandscapeLeft)
                        return 90.0;

                    if (orientation == DeviceOrientation.LandscapeRight)
                        return 270.0;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                return 0.0;
            }
        }

        /// <summary>
        /// Used by negative camera
        /// </summary>
        /// <param name="jpeg"></param>
        /// <param name="sensor"></param>
        /// <returns></returns>
        public bool SavePicture(NSData jpeg, double sensor)
        {
            if (jpeg == null)
            {
                Console.WriteLine($"SavePicture NSData is null!");
                return false;
            }

            var filename = GenerateJpgFileName();

            if (CaptureLocation == CaptureLocationType.Manual)
            {
                // Save JPEG to internal folder CaptureCustomFolder 
                var documents = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var fullFilename = System.IO.Path.Combine(documents, filename);
                //System.IO.Directory.CreateDirectory(directoryname);

                NSError err = null;
                if (jpeg.Save(fullFilename, false, out err))
                {
                    Debug.WriteLine($"SavePicture created {fullFilename}");
                    SavedFilename = fullFilename;
                    SavedRotation = (double)sensor;
                    return true;
                }
                else
                {
                    Console.WriteLine($"SavePicture error saving {fullFilename}: ", err.LocalizedDescription);
                    return false;
                }
            }
            else
            {
                // Save JPEG to photo library

                return SaveJpegToGallery(jpeg, filename, sensor, _renderer.FormsControl.CustomAlbum);
            }
        }

        public bool SaveJpegToGallery(NSData jpeg, string filename, double sensor, string customAlbum)
        {
            if (jpeg == null)
            {
                Console.WriteLine("[SaveJpegToGallery] NSData jpeg = NULL");
            }

            try
            {
                bool complete = false;
                bool hasError = false;


                var addExif = new NSMutableDictionary();
                var addTiff = new NSMutableDictionary();
                //var addDng = new NSMutableDictionary();
                NSMutableDictionary gpsData = null;

                addTiff[CGImageProperties.TIFFMake] = new NSString($"{UIDevice.CurrentDevice.Model}");
                addTiff[CGImageProperties.TIFFModel] = new NSString($"{ResStrings.OwnerTitle} {ResStrings.Camera}");
                addTiff[CGImageProperties.TIFFSoftware] = new NSString($"{ResStrings.OwnerTitle} iOS {App.BuildDesc}");

                //addDng[CGImageProperties.DNGUniqueCameraModel] = new NSString("AppoMobi App");
                //addDng[CGImageProperties.DNGLocalizedCameraModel] = new NSString("AppoMobi App L");

                addExif[ImageIO.CGImageProperties.ExifMakerNote] = new NSString(ResStrings.OwnerTitle);
                addExif[ImageIO.CGImageProperties.ExifUserComment] = new NSString(ResStrings.OwnerTitle);

                if (_renderer.FormsControl.LocationLat != 0 && _renderer.FormsControl.LocationLon != 0) //test
                {
                    // Set the GPS latitude and longitude values
                    NSNumber latitude = new NSNumber((float)_renderer.FormsControl.LocationLat);
                    NSNumber longitude = new NSNumber((float)_renderer.FormsControl.LocationLon);

                    NSString latitudeRef = (latitude.DoubleValue >= 0) ? new NSString("N") : new NSString("S");
                    NSString longitudeRef = (longitude.DoubleValue >= 0) ? new NSString("E") : new NSString("W");

                    gpsData = new NSMutableDictionary();
                    gpsData[CGImageProperties.GPSVersion] = new NSString("2.3.0.0");
                    gpsData[CGImageProperties.GPSLatitude] = latitude;
                    gpsData[CGImageProperties.GPSLatitudeRef] = latitudeRef;
                    gpsData[CGImageProperties.GPSLongitude] = longitude;
                    gpsData[CGImageProperties.GPSLongitudeRef] = longitudeRef;
                }

                void SaveToAlbum()
                {
                    //------------------------------------------------------
                    //modify exif
                    //------------------------------------------------------

                    var newJpeg = jpeg.MutableCopy() as NSMutableData;
                    using var cgImgSource = CGImageSource.FromData(jpeg);
                    var thisType = cgImgSource.TypeIdentifier;

                    var destination = CGImageDestination.Create(newJpeg, thisType, 1, null);
                    var imageProperties = cgImgSource.CopyProperties(new CGImageOptions(), 0);
                    using var mutable = imageProperties.MutableCopy() as NSMutableDictionary;

                    NSMutableDictionary
                        EXIFDictionary = null; // mutable[CGImageProperties.ExifDictionary] as NSMutableDictionary;

                    //from hardware
                    if (_props != null && _props.Exif != null && _props.Exif.Dictionary != null)
                    {
                        EXIFDictionary = _props.Exif.Dictionary.MutableCopy() as NSMutableDictionary;
                    }

                    if (EXIFDictionary == null)
                    {
                        EXIFDictionary = new();
                    }

                    //modify
                    foreach (var key in addExif.Keys)
                    {
                        Debug.WriteLine($"{key}: {addExif[key]}");
                        EXIFDictionary[key] = addExif[key];
                    }

                    if (gpsData != null)
                        mutable[CGImageProperties.GPSDictionary] = gpsData;

                    mutable[CGImageProperties.ExifDictionary] = EXIFDictionary;
                    mutable[CGImageProperties.TIFFDictionary] = addTiff;
                    //mutable[CGImageProperties.DNGDictionary] = addDng;

                    Debug.WriteLine($"RAW:");
                    foreach (var key in addTiff.Keys)
                    {
                        Debug.WriteLine($"{key}: {addTiff[key]}");
                        //mutable[key] = addRaw[key];
                    }

                    destination.AddImage(cgImgSource, 0, mutable);
                    destination.Close();

                    jpeg = newJpeg;

                    //------------------------------------------------------

                    PHPhotoLibrary.SharedPhotoLibrary.PerformChanges(() =>
                        {
                            var options = new PHAssetResourceCreationOptions { OriginalFilename = filename };

                            var creationRequest = PHAssetCreationRequest.CreationRequestForAsset();
                            creationRequest.AddResource(PHAssetResourceType.Photo, jpeg, options);

                            if (!string.IsNullOrEmpty(customAlbum))
                            {
                                var albumChangeRequest = PHAssetCollectionChangeRequest.ChangeRequest(_album);
                                albumChangeRequest.AddAssets(new[] { creationRequest.PlaceholderForCreatedAsset });
                            }
                        },
                        (ok, error) =>
                        {
                            newJpeg?.Dispose();
                            jpeg?.Dispose();

                            if (error != null)
                            {
                                Console.WriteLine($"SaveJpegToGallery  error saving {filename}: {error}");
                            }
                            else
                            {
                                SavedFilename = filename;
                                SavedRotation = (double)sensor;
                                Debug.WriteLine($"SaveJpegToGallery created {filename}");
                            }

                            complete = true;
                        });
                }

                if (_album == null && !string.IsNullOrEmpty(customAlbum))
                {
                    CreateAlbum(customAlbum, SaveToAlbum, () => { complete = true; });
                }
                else
                {
                    SaveToAlbum();
                }

                Debug.WriteLine("SaveJpegToGallery processing..");

                while (!complete)
                {
                    Task.Delay(10).Wait();
                }

                return !hasError;
            }
            catch (Exception e)
            {
                App.Logger.Error("SaveJpegToGallery", e);
                return false;
            }
            finally
            {
                Debug.WriteLine("SaveJpegToGallery returned");
            }
        }

        #endregion
    }
}
