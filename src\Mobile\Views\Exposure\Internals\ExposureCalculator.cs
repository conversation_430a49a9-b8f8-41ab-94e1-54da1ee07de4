﻿using AppoMobi.Models;
using DrawnUi.Camera;

namespace AppoMobi.Main
{
    /// <summary>
    /// Handles exposure calculations and equivalent exposure relationships
    /// </summary>
    public static class ExposureCalculator
    {
        /// <summary>
        /// Calculates Exposure Value (EV) from aperture, shutter speed, and ISO
        /// </summary>
        /// <param name="aperture">F-stop value (e.g., 2.8)</param>
        /// <param name="shutterSpeed">Shutter speed in seconds (e.g., 1/125 = 0.008)</param>
        /// <param name="iso">ISO value (e.g., 100)</param>
        /// <returns>Exposure Value</returns>
        public static double CalculateEV(double aperture, double shutterSpeed, double iso)
        {
            return Math.Log2((aperture * aperture) / shutterSpeed) + Math.Log2(iso / 100.0);
        }

        /// <summary>
        /// Converts scene brightness (lux) to target EV for proper exposure
        /// </summary>
        /// <param name="sceneBrightness">Scene brightness in lux</param>
        /// <param name="iso">Current ISO setting</param>
        /// <returns>Target EV for proper exposure</returns>
        public static double BrightnessToTargetEV(double sceneBrightness, double iso = 100)
        {
            // Standard formula: EV = log2(Luminance * ISO / K)
            // K ≈ 12.5 for reflected light meter (typical constant)
            // This gives us the EV needed for proper exposure of this brightness
            const double K = 12.5;
            return Math.Log2(sceneBrightness * iso / K);
        }



        /// <summary>
        /// Calculates required shutter speed for given aperture, ISO, and target EV
        /// </summary>
        public static double CalculateShutterSpeed(double aperture, double iso, double targetEV)
        {
            double isoFactor = iso / 100.0;
            return (aperture * aperture * isoFactor) / Math.Pow(2, targetEV);
        }

        /// <summary>
        /// Calculates required aperture for given shutter speed, ISO, and target EV
        /// </summary>
        public static double CalculateAperture(double shutterSpeed, double iso, double targetEV)
        {
            double isoFactor = iso / 100.0;
            return Math.Sqrt((shutterSpeed * Math.Pow(2, targetEV)) / isoFactor);
        }

        /// <summary>
        /// Calculates required ISO for given aperture, shutter speed, and target EV
        /// </summary>
        public static double CalculateISO(double aperture, double shutterSpeed, double targetEV)
        {
            return 100.0 * (aperture * aperture) / (shutterSpeed * Math.Pow(2, targetEV));
        }

        /// <summary>
        /// Calculates equivalent exposure settings based on target EV and locked parameters
        /// </summary>
        /// <param name="targetEV">Target exposure value from brightness measurement</param>
        /// <param name="currentSettings">Current exposure settings</param>
        /// <param name="lockedParameters">Which parameters to keep unchanged</param>
        /// <returns>New exposure settings that achieve target EV</returns>
        public static ExposureSettings CalculateEquivalentExposureFromEV(
            double targetEV,
            ExposureSettings currentSettings,
            HashSet<ExposureParameter> lockedParameters)
        {
            var result = new ExposureSettings(
                currentSettings.Aperture,
                currentSettings.ShutterSpeed,
                currentSettings.ISO);

            // Count how many parameters are locked
            var lockCount = lockedParameters.Count;

            if (lockCount == 3)
            {
                // All locked - return current settings unchanged
                return result;
            }
            else if (lockCount == 2)
            {
                // Only one parameter can change
                if (!lockedParameters.Contains(ExposureParameter.ShutterSpeed))
                {
                    result.ShutterSpeed = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                }
                else if (!lockedParameters.Contains(ExposureParameter.Aperture))
                {
                    result.Aperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                }
                else if (!lockedParameters.Contains(ExposureParameter.ISO))
                {
                    result.ISO = CalculateISO(result.Aperture, result.ShutterSpeed, targetEV);
                }
            }
            else if (lockCount == 1)
            {
                // Two parameters can change - prefer adjusting shutter speed first, then aperture
                if (lockedParameters.Contains(ExposureParameter.Aperture))
                {
                    // Aperture locked - adjust shutter, keep ISO
                    result.ShutterSpeed = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                }
                else if (lockedParameters.Contains(ExposureParameter.ShutterSpeed))
                {
                    // Shutter locked - adjust aperture, keep ISO  
                    result.Aperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                }
                else if (lockedParameters.Contains(ExposureParameter.ISO))
                {
                    // ISO locked - adjust shutter, keep aperture
                    result.ShutterSpeed = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                }
            }
            else
            {
                // Nothing locked - adjust shutter speed, keep aperture and ISO
                result.ShutterSpeed = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
            }

            return result;
        }

        /// <summary>
        /// Finds the closest value in a list to the target value
        /// </summary>
        public static int FindClosestIndex(List<ValueItem> items, double targetValue)
        {
            if (items == null || items.Count == 0) return -1;

            int closestIndex = 0;
            double minDifference = Math.Abs(items[0].Value - targetValue);

            for (int i = 1; i < items.Count; i++)
            {
                double difference = Math.Abs(items[i].Value - targetValue);
                if (difference < minDifference)
                {
                    minDifference = difference;
                    closestIndex = i;
                }
            }

            return closestIndex;
        }

        /// <summary>
        /// Calculates equivalent exposure when one parameter changes
        /// </summary>
        public static ExposureSettings CalculateEquivalentExposure(
            double currentAperture,
            double currentShutterSpeed,
            double currentISO,
            ExposureParameter lockedParameter,
            double newValue)
        {
            double currentEV = CalculateEV(currentAperture, currentShutterSpeed, currentISO);

            switch (lockedParameter)
            {
                case ExposureParameter.Aperture:
                    var newShutterForAperture = CalculateShutterSpeed(newValue, currentISO, currentEV);
                    return new ExposureSettings(newValue, newShutterForAperture, currentISO);

                case ExposureParameter.ShutterSpeed:
                    var newApertureForShutter = CalculateAperture(newValue, currentISO, currentEV);
                    return new ExposureSettings(newApertureForShutter, newValue, currentISO);

                case ExposureParameter.ISO:
                    var newShutterForISO = CalculateShutterSpeed(currentAperture, newValue, currentEV);
                    return new ExposureSettings(currentAperture, newShutterForISO, newValue);

                default:
                    return new ExposureSettings(currentAperture, currentShutterSpeed, currentISO);
            }
        }

        /// <summary>
        /// Applies exposure compensation to the calculated exposure
        /// </summary>
        public static ExposureSettings ApplyExposureCompensation(
            ExposureSettings baseExposure,
            double compensationStops,
            ExposureParameter adjustParameter = ExposureParameter.ShutterSpeed)
        {
            double newEV = CalculateEV(baseExposure.Aperture, baseExposure.ShutterSpeed, baseExposure.ISO) + compensationStops;

            switch (adjustParameter)
            {
                case ExposureParameter.ShutterSpeed:
                    var newShutter = CalculateShutterSpeed(baseExposure.Aperture, baseExposure.ISO, newEV);
                    return new ExposureSettings(baseExposure.Aperture, newShutter, baseExposure.ISO);

                case ExposureParameter.Aperture:
                    var newAperture = CalculateAperture(baseExposure.ShutterSpeed, baseExposure.ISO, newEV);
                    return new ExposureSettings(newAperture, baseExposure.ShutterSpeed, baseExposure.ISO);

                case ExposureParameter.ISO:
                    var newISO = CalculateISO(baseExposure.Aperture, baseExposure.ShutterSpeed, newEV);
                    return new ExposureSettings(baseExposure.Aperture, baseExposure.ShutterSpeed, newISO);

                default:
                    return baseExposure;
            }
        }

        /// <summary>
        /// Calculates equivalent exposure settings based on target EV and locked parameters,
        /// constrained to available wheel ranges
        /// </summary>
        public static ExposureSettings CalculateEquivalentExposureFromEVConstrained(
            double targetEV,
            ExposureSettings currentSettings,
            HashSet<ExposureParameter> lockedParameters,
            List<ValueItem> availableApertures,
            List<ValueItem> availableShutterSpeeds,
            List<ValueItem> availableISOs)
        {
            System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Input: targetEV={targetEV:F1}, current={currentSettings}, locks={string.Join(",", lockedParameters)}");

            var result = new ExposureSettings(
                currentSettings.Aperture,
                currentSettings.ShutterSpeed,
                currentSettings.ISO);

            var lockCount = lockedParameters.Count;

            if (lockCount == 3)
            {
                System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] All parameters locked, returning current settings");
                return result; // All locked - return current settings unchanged
            }
            else if (lockCount == 2)
            {
                // Only one parameter can change
                if (!lockedParameters.Contains(ExposureParameter.ShutterSpeed))
                {
                    var calculatedShutter = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                    result.ShutterSpeed = ConstrainToRange(calculatedShutter, availableShutterSpeeds);
                }
                else if (!lockedParameters.Contains(ExposureParameter.Aperture))
                {
                    var calculatedAperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                    result.Aperture = ConstrainToRange(calculatedAperture, availableApertures);
                }
                else if (!lockedParameters.Contains(ExposureParameter.ISO))
                {
                    var calculatedISO = CalculateISO(result.Aperture, result.ShutterSpeed, targetEV);
                    result.ISO = ConstrainToRange(calculatedISO, availableISOs);
                }
            }
            else if (lockCount == 1)
            {
                // Two parameters can change - try to fit within constraints
                if (lockedParameters.Contains(ExposureParameter.Aperture))
                {
                    // Aperture locked - try adjusting shutter speed first
                    var calculatedShutter = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                    if (IsInRange(calculatedShutter, availableShutterSpeeds))
                    {
                        result.ShutterSpeed = calculatedShutter;
                    }
                    else
                    {
                        // Shutter out of range, adjust both shutter and ISO
                        result.ShutterSpeed = ConstrainToRange(calculatedShutter, availableShutterSpeeds);
                        // Recalculate ISO to compensate
                        var compensatingISO = CalculateISO(result.Aperture, result.ShutterSpeed, targetEV);
                        result.ISO = ConstrainToRange(compensatingISO, availableISOs);
                    }
                }
                else if (lockedParameters.Contains(ExposureParameter.ShutterSpeed))
                {
                    // Shutter locked - try adjusting aperture first  
                    var calculatedAperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                    if (IsInRange(calculatedAperture, availableApertures))
                    {
                        result.Aperture = calculatedAperture;
                    }
                    else
                    {
                        // Aperture out of range, adjust both aperture and ISO
                        result.Aperture = ConstrainToRange(calculatedAperture, availableApertures);
                        // Recalculate ISO to compensate
                        var compensatingISO = CalculateISO(result.Aperture, result.ShutterSpeed, targetEV);
                        result.ISO = ConstrainToRange(compensatingISO, availableISOs);
                    }
                }
                else if (lockedParameters.Contains(ExposureParameter.ISO))
                {
                    // ISO locked - try adjusting shutter speed first
                    var calculatedShutter = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                    if (IsInRange(calculatedShutter, availableShutterSpeeds))
                    {
                        result.ShutterSpeed = calculatedShutter;
                    }
                    else
                    {
                        // Shutter out of range, adjust both shutter and aperture
                        result.ShutterSpeed = ConstrainToRange(calculatedShutter, availableShutterSpeeds);
                        // Recalculate aperture to compensate
                        var compensatingAperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                        result.Aperture = ConstrainToRange(compensatingAperture, availableApertures);
                    }
                }
            }
            else
            {
                // Nothing locked - adjust shutter speed first, then others if needed
                var calculatedShutter = CalculateShutterSpeed(result.Aperture, result.ISO, targetEV);
                System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] No locks: calculated shutter={calculatedShutter:F6}");

                if (IsInRange(calculatedShutter, availableShutterSpeeds))
                {
                    result.ShutterSpeed = calculatedShutter;
                    System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Shutter in range, using {calculatedShutter:F6}");
                }
                else
                {
                    result.ShutterSpeed = ConstrainToRange(calculatedShutter, availableShutterSpeeds);
                    System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Shutter out of range, constrained to {result.ShutterSpeed:F6}");

                    // Try to compensate with aperture
                    var compensatingAperture = CalculateAperture(result.ShutterSpeed, result.ISO, targetEV);
                    System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Compensating aperture={compensatingAperture:F2}");

                    if (IsInRange(compensatingAperture, availableApertures))
                    {
                        result.Aperture = compensatingAperture;
                        System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Aperture in range, using {compensatingAperture:F2}");
                    }
                    else
                    {
                        result.Aperture = ConstrainToRange(compensatingAperture, availableApertures);
                        System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Aperture out of range, constrained to {result.Aperture:F2}");

                        // Final compensation with ISO
                        var compensatingISO = CalculateISO(result.Aperture, result.ShutterSpeed, targetEV);
                        System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Final compensating ISO={compensatingISO:F0}");
                        result.ISO = ConstrainToRange(compensatingISO, availableISOs);
                        System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] ISO constrained to {result.ISO:F0}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"[CONSTRAINT] Final result: {result}, resultEV={result.GetEV():F1}");
            return result;
        }

        /// <summary>
        /// Constrains a value to the nearest available value in the list
        /// </summary>
        private static double ConstrainToRange(double value, List<ValueItem> availableValues)
        {
            if (availableValues == null || availableValues.Count == 0) return value;

            var index = FindClosestIndex(availableValues, value);
            return availableValues[index].Value;
        }

        /// <summary>
        /// Checks if a value is within the range of available values
        /// </summary>
        private static bool IsInRange(double value, List<ValueItem> availableValues)
        {
            if (availableValues == null || availableValues.Count == 0) return true;

            var minValue = availableValues.Min(x => x.Value);
            var maxValue = availableValues.Max(x => x.Value);

            return value >= minValue && value <= maxValue;
        }

    }
}
