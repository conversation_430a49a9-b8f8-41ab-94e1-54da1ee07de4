using AppoMobi.Models;
using AppoMobi.Xam;
using Newtonsoft.Json.Linq;
using static System.Runtime.InteropServices.JavaScript.JSType;


namespace AppoMobi.Main
{
    // ExposureMeter.cs

    public partial class ExposureMeter : AppScreen
    {
        public ExposureMeteringMode ModuleType { get; protected set; }

        #region CORRECTION

        public void EditCorrection()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                PopupEditCorrectionDrawn.Show(new Command((object context) =>
                    {
                        var value = (double)context;
                        Correction = value;
                        Preferences.Set($"Adjust{ModuleType}", value);
                    }),
                    ResStrings.Correction,
                    Correction);

                //var dialog = new PopupEditCorrection(new Command((object context) =>
                //    {
                //        var value = (double)context;
                //        Correction = value;
                //        Preferences.Set($"Adjust{ModuleType}", value);
                //    }),
                //    ResStrings.Correction,
                //    Correction);
                //dialog.Open();
            });
        }

        private double _Correction = 0.0;

        /// <summary>
        /// Exposure correction in EV steps (e.g., +0.5, +1.0, -1.0)
        /// Applied to the measured EV before calculating wheel positions
        /// </summary>
        public double Correction
        {
            get => _Correction;
            set
            {
                if (_Correction != value)
                {
                    _Correction = value;
                    OnPropertyChanged();
                    //ApplyCorrection();
                }
            }
        }

        #endregion

        #region SHUTTER TIME

        private int _IndexTime = -1;

        public int IndexTime
        {
            get { return _IndexTime; }
            set
            {
                if (_IndexTime != value)
                {
                    _IndexTime = value;
                    OnPropertyChanged();
                    if (!_suppressCalculations && value >= 0)
                    {
                        OnWheelChanged(ExposureParameter.ShutterSpeed);
                    }
                }
            }
        }

        public List<ValueItem> ItemsTime { get; set; }

        #endregion

        #region ISO

        private int _IndexIso = -1;

        public int IndexIso
        {
            get { return _IndexIso; }
            set
            {
                if (_IndexIso != value)
                {
                    _IndexIso = value;
                    OnPropertyChanged();
                    if (!_suppressCalculations && value >= 0)
                    {
                        OnWheelChanged(ExposureParameter.ISO);
                    }
                }
            }
        }

        public List<ValueItem> ItemsIso { get; set; }

        #endregion

        #region Aperture

        private int _IndexAperture = -1;

        public int IndexAperture
        {
            get { return _IndexAperture; }
            set
            {
                if (_IndexAperture != value)
                {
                    _IndexAperture = value;
                    OnPropertyChanged();
                    if (!_suppressCalculations && value >= 0)
                    {
                        OnWheelChanged(ExposureParameter.Aperture);
                    }
                }
            }
        }

        public List<ValueItem> ItemsAperture { get; set; }

        #endregion

        #region LOCKING AND CALCULATIONS

        private bool _suppressCalculations = false;
        private bool _suppressExposurePairs = false;
        private double _measuredEV = 0;
        private bool _hasValidMeasurement = false;
        private ExposureParameter _lockedParameter = ExposureParameter.ISO;
        private List<SkiaShape> _lockButtons = new List<SkiaShape>();

        private bool _isApertureLocked = false;

        public bool IsApertureLocked
        {
            get => _isApertureLocked;
            set
            {
                if (_isApertureLocked != value)
                {
                    if (!value && CurrentPriorityMode == ExposureMeterMode.AperturePriority)
                        return;

                    _isApertureLocked = value;
                    OnPropertyChanged();
                    if (value) // When aperture is locked, switch to A.PRI mode
                    {
                        CurrentPriorityMode = ExposureMeterMode.AperturePriority;
                    }

                    UpdateLockStates();
                }
            }
        }

        private bool _isShutterLocked = false;

        public bool IsShutterLocked
        {
            get => _isShutterLocked;
            set
            {
                if (_isShutterLocked != value)
                {
                    if (!value && CurrentPriorityMode == ExposureMeterMode.ShutterPriority)
                        return;

                    _isShutterLocked = value;
                    OnPropertyChanged();
                    if (value) // When shutter is locked, switch to S.PRI mode
                    {
                        CurrentPriorityMode = ExposureMeterMode.ShutterPriority;
                    }

                    UpdateLockStates();
                }
            }
        }

        private bool _isISOLocked = true;

        public bool IsISOLocked
        {
            get => _isISOLocked;
            set
            {
                if (_isISOLocked != value)
                {
                    _isISOLocked = value;
                    OnPropertyChanged();
                    UpdateLockStates();
                }
            }
        }

        #endregion

        #region TWEAKS

        private double _ExposureCompensation = 0.0;

        public double ExposureCompensation
        {
            get => _ExposureCompensation;
            set
            {
                if (_ExposureCompensation != value)
                {
                    _ExposureCompensation = value;
                    OnPropertyChanged();
                    ApplyExposureCompensation();
                }
            }
        }

        private MeteringMode _CurrentMeteringMode = MeteringMode.Spot;

        public MeteringMode CurrentMeteringMode
        {
            get => _CurrentMeteringMode;
            set
            {
                if (_CurrentMeteringMode != value)
                {
                    _CurrentMeteringMode = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region PRIORITY MODES

        private ExposureMeterMode _CurrentPriorityMode;

        public ExposureMeterMode CurrentPriorityMode
        {
            get => _CurrentPriorityMode;
            set
            {
                if (_CurrentPriorityMode != value)
                {
                    _CurrentPriorityMode = value;
                    OnPropertyChanged();
                    ApplyPriorityMode();
                }
            }
        }

        /// <summary>
        /// Applies the current priority mode by setting appropriate locks
        /// </summary>
        private void ApplyPriorityMode()
        {
            _suppressCalculations = true;

            // ISO is always locked in both priority modes
            IsISOLocked = true;

            switch (CurrentPriorityMode)
            {
                case ExposureMeterMode.AperturePriority:
                    // A.PRI: Lock aperture and ISO, allow shutter to adjust
                    IsApertureLocked = true;
                    IsShutterLocked = false;
                    Mode = "A MODE";
                    break;

                case ExposureMeterMode.ShutterPriority:
                    // S.PRI: Lock shutter and ISO, allow aperture to adjust
                    IsApertureLocked = false;
                    IsShutterLocked = true;
                    Mode = "T MODE";
                    break;
            }

            _suppressCalculations = false;
            UpdateLockButtonsState();
        }

        #endregion

        /// <summary>
        /// Called when user manually changes a wheel value - implements exposure pairs
        /// </summary>
        private void OnWheelChanged(ExposureParameter changedParameter)
        {
            // В СпотМетре нужно не давать крутить экспопары
            if (ModuleType == ExposureMeteringMode.Spot)
            {
                ClearResults();
                return;
            }

            if (IndexAperture < 0 || IndexTime < 0 || IndexIso < 0 || _suppressExposurePairs ||
                !_hasValidMeasurement) return;

            var currentAperture = ItemsAperture[IndexAperture].Value;
            var currentShutter = ItemsTime[IndexTime].Value;
            var currentISO = ItemsIso[IndexIso].Value;

            // Use the measured EV from the light measurement (not recalculated!)

            _suppressCalculations = true;

            switch (CurrentPriorityMode)
            {
                case ExposureMeterMode.AperturePriority:
                    // A.PRI: Aperture and ISO are priority, Shutter adjusts
                    if (changedParameter == ExposureParameter.Aperture || changedParameter == ExposureParameter.ISO)
                    {
                        // Calculate new shutter speed to maintain measured EV
                        var newShutter =
                            ExposureCalculator.CalculateShutterSpeed(currentAperture, currentISO, _measuredEV);
                        var newShutterIndex = ExposureCalculator.FindClosestIndex(ItemsTime, newShutter);
                        if (newShutterIndex >= 0 && newShutterIndex != IndexTime)
                        {
                            IndexTime = newShutterIndex;
                            System.Diagnostics.Debug.WriteLine(
                                $"[EXPOSURE PAIR] A.PRI: {changedParameter} changed → Shutter: {ItemsTime[newShutterIndex].Title} (EV: {_measuredEV:F1})");
                        }
                    }

                    break;

                case ExposureMeterMode.ShutterPriority:
                    // S.PRI: Shutter and ISO are priority, Aperture adjusts
                    if (changedParameter == ExposureParameter.ShutterSpeed || changedParameter == ExposureParameter.ISO)
                    {
                        // Calculate new aperture to maintain measured EV
                        var newAperture = ExposureCalculator.CalculateAperture(currentShutter, currentISO, _measuredEV);
                        var newApertureIndex = ExposureCalculator.FindClosestIndex(ItemsAperture, newAperture);
                        if (newApertureIndex >= 0 && newApertureIndex != IndexAperture)
                        {
                            IndexAperture = newApertureIndex;
                            System.Diagnostics.Debug.WriteLine(
                                $"[EXPOSURE PAIR] S.PRI: {changedParameter} changed → Aperture: f/{ItemsAperture[newApertureIndex].Value:F1} (EV: {_measuredEV:F1})");
                        }
                    }

                    break;
            }

            _suppressCalculations = false;
        }

        /// <summary>
        /// Updates which parameter should remain unlocked based on lock states
        /// </summary>
        private void UpdateLockStates()
        {
            // With single-lock system, this is simpler
            if (IsApertureLocked)
                _lockedParameter = ExposureParameter.Aperture;
            else if (IsShutterLocked)
                _lockedParameter = ExposureParameter.ShutterSpeed;
            else if (IsISOLocked)
                _lockedParameter = ExposureParameter.ISO;
            else
                _lockedParameter = ExposureParameter.ShutterSpeed; // Default when nothing locked

            System.Diagnostics.Debug.WriteLine($"[LOCK] Current mode: {CurrentPriorityMode}");
        }

        /// <summary>
        /// Gets currently locked parameters as a set
        /// </summary>
        private HashSet<ExposureParameter> GetLockedParameters()
        {
            var locked = new HashSet<ExposureParameter>();
            if (IsApertureLocked) locked.Add(ExposureParameter.Aperture);
            if (IsShutterLocked) locked.Add(ExposureParameter.ShutterSpeed);
            if (IsISOLocked) locked.Add(ExposureParameter.ISO);
            return locked;
        }

        /// <summary>
        /// Called when user changes an exposure parameter via wheel.
        /// We are NOT using this because we calculate ONLY when shutter BTN is pressed!!!
        /// </summary>
        private void OnExposureParameterChanged(ExposureParameter changedParameter)
        {
            if (IndexAperture < 0 || IndexTime < 0 || IndexIso < 0) return;

            var currentAperture = ItemsAperture[IndexAperture].Value;
            var currentShutter = ItemsTime[IndexTime].Value;
            var currentISO = ItemsIso[IndexIso].Value;

            var newValue = changedParameter switch
            {
                ExposureParameter.Aperture => currentAperture,
                ExposureParameter.ShutterSpeed => currentShutter,
                ExposureParameter.ISO => currentISO,
                _ => 0
            };

            var equivalentExposure = ExposureCalculator.CalculateEquivalentExposure(
                currentAperture, currentShutter, currentISO, changedParameter, newValue);

            UpdateWheelsFromExposureExcluding(equivalentExposure, changedParameter);
        }

        /// <summary>
        /// Updates wheel positions but excludes the parameter that just changed
        /// </summary>
        private void UpdateWheelsFromExposureExcluding(ExposureSettings exposure, ExposureParameter excludeParameter)
        {
            _suppressCalculations = true;
            _suppressExposurePairs = true;

            if (excludeParameter != ExposureParameter.Aperture && !IsApertureLocked)
            {
                var newApertureIndex = ExposureCalculator.FindClosestIndex(ItemsAperture, exposure.Aperture);
                if (newApertureIndex >= 0 && newApertureIndex != IndexAperture)
                    IndexAperture = newApertureIndex;
            }

            if (excludeParameter != ExposureParameter.ShutterSpeed && !IsShutterLocked)
            {
                var newShutterIndex = ExposureCalculator.FindClosestIndex(ItemsTime, exposure.ShutterSpeed);
                if (newShutterIndex >= 0 && newShutterIndex != IndexTime)
                    IndexTime = newShutterIndex;
            }

            if (excludeParameter != ExposureParameter.ISO && !IsISOLocked)
            {
                var newISOIndex = ExposureCalculator.FindClosestIndex(ItemsIso, exposure.ISO);
                if (newISOIndex >= 0 && newISOIndex != IndexIso)
                    IndexIso = newISOIndex;
            }

            _suppressCalculations = false;
            _suppressExposurePairs = false;
        }

        /// <summary>
        /// Updates wheel positions based on calculated exposure settings
        /// </summary>
        private void UpdateWheelsFromExposure(ExposureSettings newExposure)
        {
            _suppressCalculations = true;
            _suppressExposurePairs = true;

            // Update ISO wheel if not locked
            if (!IsISOLocked)
            {
                var newISOIndex = ExposureCalculator.FindClosestIndex(ItemsIso, newExposure.ISO);
                if (newISOIndex >= 0 && newISOIndex != IndexIso)
                {
                    var actualISOValue = ItemsIso[newISOIndex].Value;
                    System.Diagnostics.Debug.WriteLine(
                        $"[WHEEL ISO] Calculated: {newExposure.ISO:F0} → Selected: {actualISOValue:F0} (index {newISOIndex})");
                    IndexIso = newISOIndex;
                }
            }

            // Update aperture wheel if not locked
            if (!IsApertureLocked)
            {
                var newApertureIndex = ExposureCalculator.FindClosestIndex(ItemsAperture, newExposure.Aperture);
                if (newApertureIndex >= 0 && newApertureIndex != IndexAperture)
                {
                    var actualApertureValue = ItemsAperture[newApertureIndex].Value;
                    System.Diagnostics.Debug.WriteLine(
                        $"[WHEEL APERTURE] Calculated: f/{newExposure.Aperture:F1} → Selected: f/{actualApertureValue:F1} (index {newApertureIndex})");
                    IndexAperture = newApertureIndex;
                }
            }

            // Update shutter wheel if not locked  
            if (!IsShutterLocked)
            {
                var newShutterIndex = ExposureCalculator.FindClosestIndex(ItemsTime, newExposure.ShutterSpeed);
                if (newShutterIndex >= 0 && newShutterIndex != IndexTime)
                {
                    var actualShutterValue = ItemsTime[newShutterIndex].Value;
                    var shutterDisplay = actualShutterValue < 1
                        ? $"1/{(1 / actualShutterValue):F0}"
                        : $"{actualShutterValue:F1}s";
                    var calculatedDisplay = newExposure.ShutterSpeed < 1
                        ? $"1/{(1 / newExposure.ShutterSpeed):F0}"
                        : $"{newExposure.ShutterSpeed:F1}s";
                    System.Diagnostics.Debug.WriteLine(
                        $"[WHEEL SHUTTER] Calculated: {calculatedDisplay} → Selected: {shutterDisplay} (index {newShutterIndex})");
                    IndexTime = newShutterIndex;
                }
            }

            _suppressCalculations = false;
            _suppressExposurePairs = false;
        }

        /// <summary>
        /// Performs brightness measurement and calculates proper exposure settings
        /// </summary>
        private async Task MeasureBrightnessAsync()
        {
            if (ModuleType == ExposureMeteringMode.Spot)
            {
                ClearResults();
            }

            if (ExposureCamera == null || IndexAperture < 0 || IndexTime < 0 || IndexIso < 0) return;

            // Get current settings from picker wheels
            var currentAperture = ItemsAperture[IndexAperture].Value;
            var currentShutter = ItemsTime[IndexTime].Value;
            var currentISO = ItemsIso[IndexIso].Value;
            var currentSettings = new ExposureSettings(currentAperture, currentShutter, currentISO);

            if (ModuleType == ExposureMeteringMode.EXIF)
            {
                // EXIF mode: Use camera metadata instead of brightness measurement
                await ProcessEXIFModeAsync(currentSettings);
            }
            else
            {
                // Spot mode: Traditional brightness measurement
                await ProcessSpotModeAsync(currentSettings);
            }
        }

        /// <summary>
        /// Processes EXIF mode using camera preview frame metadata
        /// </summary>
        private async Task ProcessEXIFModeAsync(ExposureSettings currentSettings)
        {
            try
            {
                // Get camera metadata from preview frame
                var cameraMetadata = ExposureCamera.Camera.CameraDevice?.Meta;

                if (cameraMetadata == null)
                {
                    Lux = "No camera data";
                    System.Diagnostics.Debug.WriteLine("[EXIF] No camera metadata available");
                    return;
                }

                // Extract camera's current exposure settings
                var cameraISO = cameraMetadata.ISO;
                var cameraAperture = cameraMetadata.Aperture;
                var cameraShutter = cameraMetadata.Shutter;

                // Validate camera data
                if (cameraISO <= 0 || cameraAperture <= 0 || cameraShutter <= 0)
                {
                    Lux = "Invalid camera data";
                    System.Diagnostics.Debug.WriteLine(
                        $"[EXIF] Invalid camera data: ISO={cameraISO}, Aperture={cameraAperture}, Shutter={cameraShutter}");
                    return;
                }

                // Calculate EV from camera's exposure settings
                var cameraEV = Math.Log2(cameraAperture * cameraAperture / cameraShutter) +
                               Math.Log2(cameraISO / 100.0);

                // Apply exposure compensation and correction
                var targetEV = cameraEV;
                if (ExposureCompensation != 0)
                {
                    targetEV += ExposureCompensation;
                }

                if (Correction != 0)
                {
                    targetEV += Correction;
                }

                // Store the measured EV for exposure pairs
                _measuredEV = targetEV;
                _hasValidMeasurement = true;

                // Calculate new exposure settings respecting locks
                var lockedParams = GetLockedParameters();

                // For EXIF mode, start with camera's actual settings instead of current wheel positions
                var cameraSettings = new ExposureSettings(cameraAperture, cameraShutter, cameraISO);
                System.Diagnostics.Debug.WriteLine($"[EXIF] Camera settings: {cameraSettings}");
                System.Diagnostics.Debug.WriteLine($"[EXIF] Wheel settings: {currentSettings}");

                var newExposure = ExposureCalculator.CalculateEquivalentExposureFromEVConstrained(
                    targetEV, cameraSettings, lockedParams, ItemsAperture, ItemsTime, ItemsIso);

                // Update wheels to show new settings
                UpdateWheelsFromExposure(newExposure);

                // Record measurement result to history
                RecordMeasurementResult();

                // Display camera info instead of Lux
                var shutterDisplay = cameraShutter < 1 ? $"1/{1 / cameraShutter:F0}" : $"{cameraShutter:F1}s";
                Lux = $"f/{cameraAperture:F1} {shutterDisplay} ISO{cameraISO}";

                // Debug output
                System.Diagnostics.Debug.WriteLine(
                    $"[EXIF] Camera: f/{cameraAperture:F1}, {shutterDisplay}, ISO{cameraISO} → EV: {cameraEV:F1}");
                System.Diagnostics.Debug.WriteLine(
                    $"[EXIF] Target EV (compensation: {ExposureCompensation:+0.0;-0.0;0}, correction: {Correction:+0.0;-0.0;0}): {targetEV:F1}");
                System.Diagnostics.Debug.WriteLine($"[BEFORE] {currentSettings}");
                System.Diagnostics.Debug.WriteLine($"[AFTER]  {newExposure}");
                System.Diagnostics.Debug.WriteLine(
                    $"[LOCKS]  Aperture:{IsApertureLocked} Shutter:{IsShutterLocked} ISO:{IsISOLocked}");

                // Additional debug to verify EV calculation
                var verifyEV = ExposureCalculator.CalculateEV(newExposure.Aperture, newExposure.ShutterSpeed, newExposure.ISO);
                System.Diagnostics.Debug.WriteLine($"[EXIF] Verify calculated EV: {verifyEV:F1} (should match target: {targetEV:F1})");
            }
            catch (Exception ex)
            {
                Lux = "EXIF error";
                System.Diagnostics.Debug.WriteLine($"[EXIF ERROR] {ex.Message}");
            }
        }

        /// <summary>
        /// Processes traditional Spot mode using brightness measurement
        /// </summary>
        private async Task ProcessSpotModeAsync(ExposureSettings currentSettings)
        {
            // Measure scene brightness from camera (pixels lightness)
            var brightnessResult = await ExposureCamera.Camera.MeasureSceneBrightness(CurrentMeteringMode);

            if (brightnessResult.Success)
            {
                // Use that scene brightness to calculate manual camera settings
                var targetEV =
                    ExposureCalculator.BrightnessToTargetEV(brightnessResult.Brightness, currentSettings.ISO);

                // Apply exposure compensation and correction
                if (ExposureCompensation != 0)
                {
                    targetEV += ExposureCompensation;
                }

                if (Correction != 0)
                {
                    targetEV += Correction;
                }

                // Store the measured EV for exposure pairs
                _measuredEV = targetEV;
                _hasValidMeasurement = true;

                // Calculate new exposure settings respecting locks
                var lockedParams = GetLockedParameters();

                var newExposure = ExposureCalculator.CalculateEquivalentExposureFromEVConstrained(
                    targetEV, currentSettings, lockedParams, ItemsAperture, ItemsTime, ItemsIso);

                // Update wheels to show new settings
                UpdateWheelsFromExposure(newExposure);

                // Record measurement result to history
                RecordMeasurementResult();

                Lux = $"{brightnessResult.Brightness:0} Lux";

                // Debug output
                System.Diagnostics.Debug.WriteLine(
                    $"[BRIGHTNESS] {brightnessResult.Brightness:F0} lux → Target EV (compensation: {ExposureCompensation:+0.0;-0.0;0}, correction: {Correction:+0.0;-0.0;0}): {targetEV:F1}");
                System.Diagnostics.Debug.WriteLine($"[BEFORE] {currentSettings}");
                System.Diagnostics.Debug.WriteLine($"[AFTER]  {newExposure}");
                System.Diagnostics.Debug.WriteLine(
                    $"[LOCKS]  Aperture:{IsApertureLocked} Shutter:{IsShutterLocked} ISO:{IsISOLocked}");
            }
            else
            {
                Error = true;
                Lux = $"{ResStrings.Retry}";

                System.Diagnostics.Debug.WriteLine(
                    $"[ERROR] Brightness measurement failed: {brightnessResult.ErrorMessage}");
            }
        }

        /// <summary>
        /// Applies exposure compensation to current settings
        /// </summary>
        private void ApplyExposureCompensation()
        {
            if (IndexAperture < 0 || IndexTime < 0 || IndexIso < 0) return;

            var currentAperture = ItemsAperture[IndexAperture].Value;
            var currentShutter = ItemsTime[IndexTime].Value;
            var currentISO = ItemsIso[IndexIso].Value;

            var baseExposure = new ExposureSettings(currentAperture, currentShutter, currentISO);
            var compensatedExposure =
                ExposureCalculator.ApplyExposureCompensation(baseExposure, ExposureCompensation, _lockedParameter);

            UpdateWheelsFromExposure(compensatedExposure);
        }

        /// <summary>
        /// Applies correction to the last measured exposure
        /// </summary>
        private void ApplyCorrection()
        {
            if (!_hasValidMeasurement || IndexAperture < 0 || IndexTime < 0 || IndexIso < 0) return;

            // Get current settings from picker wheels
            var currentAperture = ItemsAperture[IndexAperture].Value;
            var currentShutter = ItemsTime[IndexTime].Value;
            var currentISO = ItemsIso[IndexIso].Value;
            var currentSettings = new ExposureSettings(currentAperture, currentShutter, currentISO);

            // Apply correction to the stored measured EV
            var correctedEV = _measuredEV + Correction;

            // Calculate new exposure settings respecting locks
            var lockedParams = GetLockedParameters();

            var newExposure = ExposureCalculator.CalculateEquivalentExposureFromEVConstrained(
                correctedEV, currentSettings, lockedParams, ItemsAperture, ItemsTime, ItemsIso);

            // Update wheels to show corrected settings
            UpdateWheelsFromExposure(newExposure);

            // Debug output
            System.Diagnostics.Debug.WriteLine(
                $"[CORRECTION] Applied {Correction:+0.0;-0.0;0} EV correction to measured EV {_measuredEV:F1} → Target EV: {correctedEV:F1}");
        }

        /// <summary>
        /// Records the measurement result to history in format "{iso} {aperture} {shutter}"
        /// Called only when a measurement is completed and final exposure values are suggested
        /// </summary>
        private void RecordMeasurementResult()
        {
            if (IndexAperture < 0 || IndexTime < 0 || IndexIso < 0) return;

            try
            {
                // Get current wheel values (the suggested exposure from measurement)
                var currentISO = (int)ItemsIso[IndexIso].Value;
                var currentAperture = ItemsAperture[IndexAperture].Value;
                var currentShutter = ItemsTime[IndexTime].Value;

                // Format shutter speed display
                var shutterDisplay = currentShutter < 1
                    ? $"1/{1 / currentShutter:F0}"
                    : $"{currentShutter:F1}s";

                AddResult($"{currentISO}".Replace(",", "."), $"{currentAperture:F1}".Replace(",", "."),
                    shutterDisplay.Replace(",", "."));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MEASUREMENT ERROR] Failed to record measurement: {ex.Message}");
            }
        }

        string _lux;

        public string Lux
        {
            get { return _lux; }
            set
            {
                if (value != _lux)
                {
                    _lux = value;
                    OnPropertyChanged();
                }
            }
        }

        string _mode;

        public string Mode
        {
            get { return _mode; }
            set
            {
                if (value != _mode)
                {
                    _mode = value;
                    OnPropertyChanged();
                }
            }
        }

        bool _isMeasuring;

        public bool IsBusy
        {
            get { return _isMeasuring; }
            set
            {
                if (value != _isMeasuring)
                {
                    _isMeasuring = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Sets default exposure values (typical daylight exposure)
        /// </summary>
        private void SetDefaultValues()
        {
            _suppressCalculations = true;
            _suppressExposurePairs = true;

            Correction = Preferences.Get($"Adjust{ModuleType}", 0.0);

            IndexAperture = ExposureCalculator.FindClosestIndex(ItemsAperture, 5.6);
            IndexTime = ExposureCalculator.FindClosestIndex(ItemsTime, 1.0 / 125);
            IndexIso = ExposureCalculator.FindClosestIndex(ItemsIso, 100);

            // Initialize priority mode (this will set the appropriate locks)
            CurrentPriorityMode = ExposureMeterMode.AperturePriority;

            _suppressCalculations = false;
            _suppressExposurePairs = false;
        }


        /// <summary>
        /// MOCK METHOD: Measures scene brightness from camera
        /// </summary>
        private async Task<BrightnessResult> MeasureSceneBrightnessMock(MeteringMode meteringMode)
        {
            // Simulate different lighting conditions for testing
            var random = new Random();
            var scenarios = new[]
            {
                new { Name = "Bright Sunlight", Lux = 100000.0 }, new { Name = "Cloudy Day", Lux = 10000.0 },
                new { Name = "Indoor Light", Lux = 500.0 }, new { Name = "Candlelight", Lux = 10.0 },
                new { Name = "Office Light", Lux = 400.0 }
            };

            await Task.Delay(100); // Simulate camera measurement delay

            var scenario = scenarios[random.Next(scenarios.Length)];

            System.Diagnostics.Debug.WriteLine(
                $"--------------------------------------------------------------------------------------");
            System.Diagnostics.Debug.WriteLine($"[CAMERA] Measuring brightness: {scenario.Name} ({scenario.Lux} lux)");

            return new BrightnessResult { Success = true, Brightness = scenario.Lux };

            // REAL IMPLEMENTATION WOULD BE:
            // #if IOS
            //     return await iOSCamera.MeasureBrightness(meteringMode);
            // #elif ANDROID  
            //     return await AndroidCamera.MeasureBrightness(meteringMode);
            // #endif
        }
    }
}
