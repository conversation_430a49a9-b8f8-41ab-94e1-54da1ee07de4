﻿namespace AppoMobi.Main
{
    public class SkiaStepper : SkiaLayout
    {

        /*
                <controls:SkiaStepper
               Grid.ColumnSpan="3"
               CurrentStep="{Binding Source={x:Reference Carousel}, Path=SelectedIndex}"
               HeightRequest="24"
               HorizontalOptions="Fill"
               ThumbColor="{StaticResource ColorPrimary}"
               TotalSteps="3"
               TrackBackgroundColor="{StaticResource ColorPath}"
               TrackForegroundColor="{StaticResource ColorPrimary}" />
         */

        public SkiaControl TrackBackground { get; set; }

        public SkiaControl TrackForeground { get; set; }

        public SkiaControl Indicator { get; set; }

        public virtual void ApplyProperties()
        {
            FindViews();

            if (Width > 0 && TrackForeground != null && TotalSteps > 0)
            {
                var stepWidth = this.Width / TotalSteps;
                var currentWidth = 0.0;
                if (CurrentStep >= 0)
                {
                    currentWidth = stepWidth * CurrentStep;
                }
                TrackForeground.WidthRequest = currentWidth;
                if (Indicator != null)
                {
                    Indicator.TranslationX = currentWidth - ThumbHeight;
                }
            }
        }

        public virtual void FindViews()
        {
            if (TrackBackground == null)
            {
                TrackBackground = FindViewByTag("TrackBackground");
            }
            if (TrackForeground == null)
            {
                TrackForeground = FindViewByTag("TrackForeground");
            }
            if (Indicator == null)
            {
                Indicator = FindViewByTag("Indicator");
            }
        }

        protected override void CreateDefaultContent()
        {
            base.CreateDefaultContent();

            if (Views.Count == 0)
            {
                SetupDefaultViews();

                ApplyProperties();
            }
        }

        protected override void OnLayoutChanged()
        {
            base.OnLayoutChanged();

            ApplyProperties();
        }



        public virtual void SetupDefaultViews()
        {
            TrackBackground = new SkiaShape()
            {
                CornerRadius = 5,
                VerticalOptions = LayoutOptions.Center,
                HeightRequest = TrackHeight,
                BackgroundColor = TrackBackgroundColor,
                HorizontalOptions = LayoutOptions.Fill
            };

            TrackForeground = new SkiaShape()
            {
                CornerRadius = 5,
                VerticalOptions = LayoutOptions.Center,
                HeightRequest = TrackHeight,
                BackgroundColor = TrackForegroundColor,
                HorizontalOptions = LayoutOptions.Fill
            };

            Indicator = new SkiaShape()
            {
                ZIndex = 1,
                LockRatio = 1,
                Type = ShapeType.Circle,
                VerticalOptions = LayoutOptions.Center,
                HeightRequest = ThumbHeight,
                BackgroundColor = ThumbColor,
            };

            this.Children.Add(TrackBackground);
            this.Children.Add(TrackForeground);
            this.Children.Add(Indicator);
        }



        private static void OnPropertiesChaged(BindableObject bindable, object oldvalue, object newvalue)
        {
            if (bindable is SkiaStepper control)
            {
                control.ApplyProperties();
            }
        }

        public static readonly BindableProperty TotalStepsProperty = BindableProperty.Create(
            nameof(TotalSteps),
            typeof(int),
            typeof(SkiaStepper),
            0, propertyChanged: OnPropertiesChaged);

        public int TotalSteps
        {
            get { return (int)GetValue(TotalStepsProperty); }
            set { SetValue(TotalStepsProperty, value); }
        }

        public static readonly BindableProperty CurrentStepProperty = BindableProperty.Create(
            nameof(CurrentStep),
            typeof(int),
            typeof(SkiaStepper),
            0, propertyChanged: OnPropertiesChaged);

        public int CurrentStep
        {
            get { return (int)GetValue(CurrentStepProperty); }
            set { SetValue(CurrentStepProperty, value); }
        }

        public static readonly BindableProperty TrackHeightProperty = BindableProperty.Create(
            nameof(TrackHeight),
            typeof(double),
            typeof(SkiaStepper),
            4.0, propertyChanged: OnPropertiesChaged);

        public double TrackHeight
        {
            get { return (double)GetValue(TrackHeightProperty); }
            set { SetValue(TrackHeightProperty, value); }
        }

        public static readonly BindableProperty ThumbHeightProperty = BindableProperty.Create(
            nameof(ThumbHeight),
            typeof(double),
            typeof(SkiaStepper),
            8.0, propertyChanged: OnPropertiesChaged);

        public double ThumbHeight
        {
            get { return (double)GetValue(ThumbHeightProperty); }
            set { SetValue(ThumbHeightProperty, value); }
        }


        public static readonly BindableProperty TrackBackgroundColorProperty = BindableProperty.Create(
            nameof(TrackBackgroundColor),
            typeof(Color),
            typeof(SkiaStepper),
            Colors.Gray, propertyChanged: OnPropertiesChaged);

        public Color TrackBackgroundColor
        {
            get { return (Color)GetValue(TrackBackgroundColorProperty); }
            set { SetValue(TrackBackgroundColorProperty, value); }
        }

        public static readonly BindableProperty TrackForegroundColorProperty = BindableProperty.Create(
            nameof(TrackForegroundColor),
            typeof(Color),
            typeof(SkiaStepper),
            Colors.Blue, propertyChanged: OnPropertiesChaged);

        public Color TrackForegroundColor
        {
            get { return (Color)GetValue(TrackForegroundColorProperty); }
            set { SetValue(TrackForegroundColorProperty, value); }
        }

        public static readonly BindableProperty ThumbColorProperty = BindableProperty.Create(
            nameof(ThumbColor),
            typeof(Color),
            typeof(SkiaStepper),
            Colors.Red, propertyChanged: OnPropertiesChaged);

        public Color ThumbColor
        {
            get { return (Color)GetValue(ThumbColorProperty); }
            set { SetValue(ThumbColorProperty, value); }
        }


    }
}
