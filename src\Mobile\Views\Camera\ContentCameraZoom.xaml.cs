﻿using AppoMobi.Forms.Content.Camera.Controls;
using AppoMobi.Forms.Content.Camera.Models;
using AppoMobi.Forms.Controls;
using AppoMobi.Forms.Controls.Skia;
using AppoMobi.Forms.Models;
using AppoMobi.Main;
using AppoMobi.Pages;
using AppoMobi.Services;
using AppoMobi.Touch;
using AppoMobi.UI;
using AppoMobi.Xam;
using DrawnUi.Extensions;
using Newtonsoft.Json;
using System.Collections;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;
using System.Windows.Input;
using System.Xml;
using SkiaLabel = AppoMobi.Forms.Controls.Skia.SkiaLabel;

namespace AppoMobi.Forms.Content.Camera
{
    public partial class ContentCameraZoom
    {

        #region CAMERA LOGIC

        private void OnTapped_CapturePhoto(object sender, TapEventArgs e)
        {
            if (Camera.IsTakingPhoto)
                return;

            Task.Run(async () =>
            {
                await Camera.RefreshLocation(500);

                Debug.WriteLine($"[CAMERA-Zoomed] Taking picture..");
                var bitmap = await Camera.PlatformRenderer.TakePictureForSkia();
                Debug.WriteLine($"[CAMERA-Zoomed] Got picture: {bitmap != null}");
                if (bitmap != null)
                {
                    //setup canvas
                    var maxCanvasDimension = Math.Min(Camera.CaptureWidth, Camera.CaptureHeight);
                    var rescaleCanvas = 1.0;
                    var viewportsDifference = 1.0;
                    if (Camera.CaptureWidth == maxCanvasDimension)
                    {
                        //rescale upon width
                        viewportsDifference = maxCanvasDimension / (double)Camera.PreviewHeight;
                        rescaleCanvas = viewportsDifference * ViewportFrames.Density * Camera.ViewportScale;
                    }
                    else
                    {
                        //rescale upon height
                        viewportsDifference = maxCanvasDimension / (double)Camera.PreviewWidth;
                        rescaleCanvas = viewportsDifference * ViewportFrames.Density * Camera.ViewportScale;
                    }
                    var scalePhoto = 1.0;
                    var zoomCapturedPhotoX = Camera.TextureScale;
                    var zoomCapturedPhotoY = Camera.TextureScale;

                    //Console.WriteLine($"[CAMERA] Saving [{Camera.SavedRotation}] - [{Camera.SavedFilename}]");
                    try
                    {

                        var stream = await ViewportFrames.RenderOverPhoto(
                            bitmap,
                            rescaleCanvas, rescaleCanvas,
                            Camera.PreviewWidth, Camera.PreviewHeight,
                            zoomCapturedPhotoX, zoomCapturedPhotoY, Camera.SavedRotation);

                        if (stream != null)
                        {
                            var filenameOutput = GenerateJpgFileName();

                            var ok = await Camera.PlatformRenderer.SaveJpgStreamToGallery(stream, filenameOutput, Camera.SavedRotation, null);

                            stream.Dispose();

                            if (ok)
                            {
                                Debug.WriteLine($"[CAMERA-Zoomed] Rendered photo: {filenameOutput}");
                                return;
                            }
                        }

                        Debug.WriteLine($"[CAMERA-Zoomed] Failed to render and save photo.");
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                    finally
                    {
                        Camera.IsTakingPhoto = false;
                    }
                }

                Camera.IsTakingPhoto = false;

            }).ConfigureAwait(false);

            //legacy using file
            //Camera.CaptureCustomFolder = FileSystem.AppDataDirectory;
            //Camera.CaptureLocation = CaptureLocationType.Manual;
            //Camera.CommandToRenderer = "TakePictureHigh";
        }

        void CapturePhoto()
        {
            Debug.WriteLine($"[CAMERA-Zoomed] Took photo for processing: {Camera.SavedFilename}");

            Camera.CaptureLocation = CaptureLocationType.Gallery; //restore

            var maxCanvasDimension = Math.Min(Camera.CaptureWidth, Camera.CaptureHeight);

            //var PreviewAspect = (double)Camera.PreviewWidth / (double)Camera.PreviewHeight;
            //var CaptureRatio = (double)Camera.CaptureWidth / (double)Camera.CaptureHeight;

            var rescaleCanvas = 1.0;
            var viewportsDifference = 1.0;
            if (Camera.CaptureWidth == maxCanvasDimension)
            {
                //rescale upon width
                viewportsDifference = maxCanvasDimension / (double)Camera.PreviewHeight;
                rescaleCanvas = viewportsDifference * ViewportFrames.Density * Camera.ViewportScale;
            }
            else
            {
                //rescale upon height
                viewportsDifference = maxCanvasDimension / (double)Camera.PreviewWidth;
                rescaleCanvas = viewportsDifference * ViewportFrames.Density * Camera.ViewportScale;
            }

            var scalePhoto = 1.0;
            //fill canvas

            var zoomCapturedPhotoX = Camera.TextureScale;
            var zoomCapturedPhotoY = Camera.TextureScale;

            Task.Run(async () =>
            {
                //Console.WriteLine($"[CAMERA] Saving [{Camera.SavedRotation}] - [{Camera.SavedFilename}]");
                try
                {

                    var stream = await ViewportFrames.RenderOverPhoto(
                        Camera.SavedFilename,
                        rescaleCanvas, rescaleCanvas,
                        Camera.PreviewWidth, Camera.PreviewHeight,
                        zoomCapturedPhotoX, zoomCapturedPhotoY, Camera.SavedRotation);

                    //delete tmp file
                    var originalFile = Camera.SavedFilename;

                    if (stream != null)
                    {
                        var filenameOutput = GenerateJpgFileName();

                        Camera.IsTakingPhoto = false;

                        var ok = await Camera.PlatformRenderer.SaveJpgStreamToGallery(stream, filenameOutput, Camera.SavedRotation, originalFile);

                        File.Delete(originalFile);

                        stream.Dispose();

                        if (ok)
                        {
                            Debug.WriteLine($"[CAMERA-Zoomed] Rendered photo: {filenameOutput}");
                            return;
                        }
                    }

                    File.Delete(originalFile);

                    Debug.WriteLine($"[CAMERA-Zoomed] Failed to render and save photo.");
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    Camera.IsTakingPhoto = false;
                }



            }).ConfigureAwait(false);

        }

        public static string GenerateJpgFileName()
        {
            var add = $"{DateTime.Now:MM/dd/yyyy HH:mm:ss}{filenamesCounter}";
            var filename = $"viewfinder-{add.Replace("/", "").Replace(":", "").Replace(" ", "").Replace(",", "").Replace(".", "").Replace("-", "")}.jpg";

            filenamesCounter++;

            return filename;
        }

        public void InitializeFramesOverlay()
        {
            var labelPreset = new SkiaLabel
            {
                TypeFace = ViewportFrames.TypeFace,
                //                Text = $"{CurrentFormat.Title}",
                //Text = $"{CurrentFocalLength35mm:0} mm",
                FontSize = 14,
                HorizontalOptions = LayoutOptions.Start,
                VerticalOptions = LayoutOptions.Start,
                Margin = new Thickness(28),
                TextColor = Colors.GreenYellow,
                TextStrokeColor = Color.Parse("#99003300"),
                RotateLayoutParameters = false
            };

            var labelFocal = new SkiaLabel
            {
                TypeFace = ViewportFrames.TypeFace,
                Text = $"{CurrentFocalLengthPreset:0} mm",
                FontSize = 14,
                HorizontalOptions = LayoutOptions.End,
                VerticalOptions = LayoutOptions.Start,
                Margin = new Thickness(28),
                TextColor = Colors.GreenYellow,
                //TextStrokeColor = ViewportFrames.ColorOutline.ToFormsColor(),
                TextStrokeColor = Color.Parse("#99003300"),
                RotateLayoutParameters = false
            };

            var labelAdjust = new SkiaLabel
            {
                TypeFace = ViewportFrames.TypeFace,
                FontSize = 13,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Center,
                Margin = new Thickness(28),
                //   TextColor = Colors.GreenYellow,
                //TextStrokeColor = ViewportFrames.ColorOutline.ToFormsColor(),
                TextStrokeColor = Color.Parse("#99003300"),
                RotateLayoutParameters = false
            };

            var labelWarning = new SkiaLabel
            {
                TypeFace = ViewportFrames.TypeFace,
                Text = ResStrings.NoLensAdded.ToUpper(),
                FontSize = 14,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Start,
                Margin = new Thickness(28),
                TextColor = Colors.Red,
                TextStrokeColor = Color.Parse("#99330000"),
                RotateLayoutParameters = true
            };

            ViewportFrames.RenderOverlay = (SKImageInfo info, SKCanvas canvas, double scale, double sensor) =>
            {
                labelPreset.SensorRotation = sensor;
                labelFocal.SensorRotation = sensor;
                labelPreset.Text = $"{CurrentFormat.Title}";
                labelFocal.Text = $"{CurrentFocalLengthPreset:0} mm";

                //labelWarning = new SkiaLabel
                //{
                //    Text = $"{Camera.SensorRotation} rotation {Camera.SensorRotation}",
                //    FontSize = 13,
                //    HorizontalOptions = LayoutOptions.Center,
                //    VerticalOptions = LayoutOptions.Start,
                //    Margin = new Thickness(28),
                //    TextColor = Colors.Red,
                //    TextStrokeColor = ViewportFrames.ColorOutline.ToFormsColor(),
                //    SensorRotation = this.Camera.SensorRotation,
                //    RotateLayoutParameters = false
                //};

                /*
                if (Camera.SensorRotation == 180)
                {
                        labelPreset.HorizontalOptions = LayoutOptions.End;
                        labelFocal.HorizontalOptions = LayoutOptions.Start;

                        HotspotPreset.HorizontalOptions = LayoutOptions.End;
                        HotspotFocal.HorizontalOptions = LayoutOptions.Start;
                }
                else
                {
                    HotspotPreset.HorizontalOptions = LayoutOptions.Start;
                    HotspotFocal.HorizontalOptions = LayoutOptions.End;
                }
                */

                labelPreset.Render(info, canvas, (float)scale);
                labelFocal.Render(info, canvas, (float)scale);

                if (!ViewportFrames.ItemsSource.Any())
                {
                    labelWarning.SensorRotation = sensor;
                    labelWarning?.Render(info, canvas, (float)scale);
                }

                if (ShowSettings)
                {
                    labelAdjust.SensorRotation = sensor;
                    labelAdjust.Text = ResStrings.Adjustment + $" {Camera.FocalLengthAdjustment:0} mm";
                    labelAdjust?.Render(info, canvas, (float)scale);
                }

            };

        }

        private void OnCameraOnPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "Zoom")
            {
                ZoomDesc = $"{Camera.Zoom:0.0}x";
            }
            else
            if (e.PropertyName == "SavedFilename")
            {
                if (Camera.IsTakingPhoto && DeviceInfo.Current.Platform == DevicePlatform.Android) //todo check this shit
                {
                    CapturePhoto();
                }
            }
            else
            if (e.PropertyName == "State")
            {
                Debug.WriteLine($"[CAMERA-Zoomed] State: {Camera.State}");
            }
            else
            if (e.PropertyName == "CameraDevice")
            {
                if (Camera.CameraDevice != null)
                {
                    SaveCameraDevice(Camera.CameraDevice);

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        // Update the UI
                        ApplyCurrentPreset();
                        InitializeFramesOverlay();
                    });
                };


            }
            else
            if (e.PropertyName == "FocalLength")
            {
                UpdateFocalLength();
            }
            else
            if (e.PropertyName == "Filter")
            {
                OnPropertyChanged("IconEffect");
            }
        }

        /// <summary>
        /// Uses CurrentPreset for internal input
        /// </summary>
        public void ApplyCurrentPreset()
        {
            //read from settings
            //todo
            if (Camera == null || Camera.CameraDevice == null)
                return;

            PresetFrames.SafeClear();

            CurrentFormat = App.ViewFinderData.GetPresetData(CurrentPreset);

            if (CurrentFormat == null || SelectedPresetInfo == null)
            {
                Console.WriteLine($"[CAMERA] Preset '{CurrentPreset}' not found!");
                return;
            }

            Camera.FocalLengthAdjustment = Settings.Current.GetValueOrDefault($"Adjustment_{CurrentFormat.Id}", 0.0);

            foreach (var lensDistance in CurrentFormat.FocalDistances)
            {
                var focalPreset = Reflection.Clone(CurrentFormat);
                focalPreset.FocalDistances = new int[] { lensDistance };

                if (Camera.CameraDevice.SensorWidth < Camera.CameraDevice.SensorHeight)
                {
                    var h = focalPreset.Height;
                    var w = focalPreset.Width;

                    focalPreset.Height = w;
                    focalPreset.Width = h;
                }

                var frame = new PresetViewport
                {
                    FrameColor = Colors.Red
                };
                frame.Init(focalPreset);

                PresetFrames.Add(frame);
            }


            //frame = new PresetViewport
            //{
            //    FrameColor = Colors.BlueViolet
            //};
            //frame.Init(App.ViewFinderData.GetPresetData("1012"));
            //PresetFrames.Add(frame);


            UpdateFocalLength();

            ViewportFrames?.Update();

        }


        protected void SetEffect(CameraEffect value)
        {
            Camera.Filter = value;

            switch (value)
            {
            case CameraEffect.ColorNegativeAuto:
            case CameraEffect.ColorNegativeManual:
            ShowPresets = true;
            break;
            default:
            ShowPresets = false;
            break;
            }

            OnPropertyChanged("ModeDesc");
            OnPropertyChanged("ShowPickers");
            Settings.Current.AddOrUpdateValue($"{this.GetType().Name}_CameraFilter", (int)Camera.Filter);
        }

        private void OnTapped_Preset(object sender, TapEventArgs e)
        {

            if (Camera.ColorPreset == 0)
            {
                Camera.ColorPreset = 1;
            }
            else
                Camera.ColorPreset = 0;
            Settings.Current.AddOrUpdateValue("CameraColorPreset", Camera.ColorPreset);

            switch (Camera.ColorPreset)
            {
            case 1:
            SetEffect(CameraEffect.ColorNegativeManual);
            break;
            default:
            SetEffect(CameraEffect.ColorNegativeAuto);
            break;
            }

        }

        public void CycleFrames(bool next = true)
        {
            if (PresetFrames == null || !PresetFrames.Any())
                return;

            var index = 0;
            var existing = PresetFrames.FirstOrDefault(x => x.Preset.FocalDistance == lastCycledFrame);
            if (existing != null)
            {
                index = PresetFrames.IndexOf(existing);
                if (next)
                {
                    if (PresetFrames.Count > index + 1)
                    {
                        index++;
                    }
                    else
                    {
                        index = 0;
                    }
                }
            }
            var selected = PresetFrames[index];

            lastCycledFrame = selected.Preset.FocalDistance;

            Camera.FocalLength = lastCycledFrame * CurrentFormat.CropFactor - Camera.FocalLengthAdjustment;

            //UpdateFocalLength();
            ViewportFrames?.Update();
        }



        public double CurrentFocalLengthPreset
        {
            get
            {
                if (Camera?.CameraDevice == null)
                    return 0;

                if (CurrentFormat == null
                    || Camera.CameraDevice.SensorCropFactor == 0.0)
                    return Camera.FocalLengthAdjusted;

                return Camera.FocalLengthAdjusted / CurrentFormat.CropFactor;
            }
        }


        private void ViewportFrames_OnOnInitialized(object sender, EventArgs e)
        {
            Device.StartTimer(TimeSpan.FromSeconds(1), () =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    CycleFrames(false);
                });
                return false;
            });
        }


        #endregion

        #region VIEW LOGIC




        public void Startup()
        {
            if (lockStartup)
            {
                Debug.WriteLine("[VIEWFINDER] Startup locked.");
                return;
            }

            lockStartup = true;

            try
            {
                Debug.WriteLine("[VIEWFINDER] Requesting permissions...");

                CameraPreview.CheckPermissions(() =>
                    {
                        Debug.WriteLine("[VIEWFINDER] Starting..");

                        PermissionsWarning = false;
                        Camera.IsEnabled = true;
                        Camera.SendCommandToRenderer("Resume");

                        if (Camera.Geotag)
                            Camera.CommandGetLocation.Execute(null);
                        else
                        {
                            Camera.CanDetectLocation = false;
                        }
                    },
                    () =>
                    {
                        Debug.WriteLine("[VIEWFINDER] Permissions denied");
                        Camera.IsEnabled = false;
                        PermissionsWarning = true;
                    }
                );


            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                Tasks.StartDelayed(TimeSpan.FromSeconds(2), () =>
                {
                    Debug.WriteLine("[VIEWFINDER] Startup UNlocked.");
                    lockStartup = false;
                });
            }
        }


        public void UpdateFocalLength()
        {
            OnPropertyChanged("CurrentFocalLength35mm");
            OnPropertyChanged("CurrentFocalLengthPreset");
        }

        protected override void OnDisposing()
        {
            Camera.PropertyChanged -= OnCameraOnPropertyChanged;

            Camera.Destroy();

            base.OnDisposing();
        }


        public ICommand CommandCheckPermissions
        {
            get
            {
                return new Command((object context) =>
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        Startup();
                    });
                });
            }
        }



        public ContentCameraZoom(IPageEnhancedNav daddy)
        {
            try
            {
                InitializeComponent(); 

                Init(daddy);

                //fill picker
                PresetsList = new List<string>();
                //PresetsList.Add(ResStrings.None);
                foreach (var item in App.ViewFinderData.Presets)
                {
                    PresetsList.Add(item.Title);
                }

                FogEnabled = Settings.Current.GetValueOrDefault($"{this.GetType().Name}_Fog", true);
                CurrentPreset = Settings.Current.GetValueOrDefault($"{this.GetType().Name}_Preset", "35");

                Model = MainVModel.Instance;
                BindingContext = Model;

                ApplyCurrentPreset();

                Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
                Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

                //Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
                //Daddy.ToggleButtonVisibility(ButtonType.Right2, true);
                //Camera.FullScreen = Settings.Current.GetValueOrDefault("CameraDisplayMode", true);

                var gamma = Settings.Current.GetValueOrDefault("CameraGamma", 1.0);
                SetGamma(gamma);

                Camera.ColorPreset = Settings.Current.GetValueOrDefault("CameraColorPreset", 0);

                Camera.PropertyChanged += OnCameraOnPropertyChanged;

                var filter = (CameraEffect)Settings.Current.GetValueOrDefault($"{this.GetType().Name}_CameraFilter", (int)CameraEffect.Positive);
                SetEffect(filter);


                if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                {
                    //we restore the calibrated device to avoid recalibrating every time
                    //as apple dos not provide camera sensor info out of the box
                    var device = RestoreSaveCameraDevice();
                    if (device != null)
                    {
                        Camera.CameraDevice = device;
                    }
                    //else
                    //{
                    //    Toast.ShortMessage("Camera calibrated");
                    //}

                }

            }
            catch (Exception e)
            {
                App.Logger.Error(this.GetType().Name, e);
                throw;
            }




            //disabled
            //Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
            //Daddy.ToggleButtonVisibility(ButtonType.Right2, true);

            //if (Daddy is WrapContent)
            //{
            //    if (((WrapContent) Daddy).TabActive)
            //    {
            //        OnTabActivated();
            //    }
            //}
        }

        bool _subscribed;

        void Subscribe()
        {
            if (!_subscribed)
            {
                _subscribed = true;
                App.Instance.Messager.Subscribe<string>(this, "Settings", async (sender, arg) =>
                {

                    if (arg == "Apply")
                    {
                        CheckPermissions();

                        return;
                    }
                });
            }
        }

        void Unsubscribe()
        {
            _subscribed = false;
            App.Instance.Messager.Unsubscribe(this, "Settings");
        }

        void CheckPermissions()
        {
            if (Daddy == null)
                return;

            if (!Daddy.IsInTabs)
            {
                CommandCheckPermissions.Execute(null);
                //todo disable menu gesture
            }
            else
            {
                if (Daddy.IsActiveTab)
                {
                    CommandCheckPermissions.Execute(null);
                    //todo disable menu gesture
                }
            }

        }

        public override void OnTabActivated()
        {
            Debug.WriteLine("[VIEWFINDER] OnTabActivated");

            base.OnTabActivated();

            if (Daddy == null)
                return;

            Subscribe();
        }

        public override void OnAppearing()
        {
            Debug.WriteLine("[VIEWFINDER] OnAppearing");

            base.OnAppearing();

            CheckPermissions();

            Subscribe();


            //else
            //{
            //    //this is called for tabs when they are visible after page over is closed..
            //    if (Daddy.ActiveTab)
            //    {
            //        CommandCheckPermissions.Execute(null);
            //    }
            //}

        }



        public override void OnTabDeactivated()
        {
            Debug.WriteLine("[VIEWFINDER] OnTabDeactivated");

            base.OnTabDeactivated();

            Camera.SendCommandToRenderer("HardPause");

            GC.Collect();
            Unsubscribe();
            //todo enable menu gesture
        }

        public override void OnDisappearing()
        {
            Debug.WriteLine("[VIEWFINDER] OnDisappearing");


            base.OnDisappearing();

            //case we are already disposed
            if (Daddy == null)
                return;

            //     if (!Daddy.IsInTabs)
            Camera.SendCommandToRenderer("HardPause");

            //GC.Collect();
            Unsubscribe();
            //todo enable menu gesture
        }



        #endregion

        #region Options


        #region PICKER PRESETS

        private ICommand commandPickerComplete;

        private void OnIndexChanged_PresetFilter(object sender, int e)
        {
            commandPickerComplete?.Execute(e);



        }

        public override void OnRightIcon1Clicked()
        {
            Core.ShowHelp(ResStrings.CameraZoomHelp);

            base.OnRightIcon1Clicked();
        }

        public override void OnRightIcon2Clicked()
        {
            base.OnRightIcon2Clicked();

            StartEditPresets();
        }

        private void OnTapped_Settings(object sender, TapEventArgs tapEventArgs)
        {
            this.ShowSettings = true;
            CycleFrames(false);
            //ControlPresetPickerPresets.DisableSpamClicks();

            //StartEditPresets();
        }

        private void OnTapped_Close(object sender, TapEventArgs e)
        {
            this.ShowSettings = false;
            CycleFrames(false);
        }


        #endregion

        #region PICKER PRESETS VIEWMODEL


        //PICKER TITLES
        public List<string> PresetsList
        {
            get { return _PresetsList; }
            set
            {
                if (_PresetsList != value)
                {
                    _PresetsList = value;
                    OnPropertyChanged();
                }
            }
        }
        private List<string> _PresetsList;


        private ViewfinderPreset _SelectedPresetInfo;
        public ViewfinderPreset SelectedPresetInfo
        {
            get { return _SelectedPresetInfo; }
            set
            {
                if (_SelectedPresetInfo != value)
                {
                    _SelectedPresetInfo = value;
                    OnPropertyChanged();

                    //todo
                    OnPropertyChanged("ResultDesc");
                    OnPropertyChanged("ResultMs");

                }
            }
        }



        public void SaveCameraDevice(CameraUnit unit)
        {
            Settings.Current.AddOrUpdateValue("CameraZoomDevice", JsonConvert.SerializeObject(unit));
        }

        public CameraUnit RestoreSaveCameraDevice()
        {

            try
            {
                var json = Settings.Current.GetValueOrDefault("CameraZoomDevice", "");
                if (!string.IsNullOrEmpty(json))
                {
                    var unit = JsonConvert.DeserializeObject<CameraUnit>(json);
                    return unit;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return null;
        }


        #endregion




        public static List<OptionsListItem> OptionList = new List<OptionsListItem>();

        public static List<OptionsListItem> OptionPresets = new List<OptionsListItem>();

        public static List<OptionsListItem> OptionEditPresets = new List<OptionsListItem>();

        public async void PickAndSetPreset()
        {
            OptionPresets.Clear();
            foreach (var preset in App.ViewFinderData.Presets.OrderByDescending(o => o.Id == CurrentPreset))
            {
                OptionPresets.Add(new OptionsListItem
                {
                    Tag = preset.Id,
                    ActionDesc = preset.Title,
                    SwitchValue = preset.Id == CurrentPreset,
                    Switched = (e) =>
                    {
                        CurrentPreset = preset.Id;
                        SelectedPresetInfo = preset;
                        ApplyCurrentPreset();
                        CycleFrames(false);
                        if (!ViewportFrames.ItemsSource.Any())
                        {
                            StartEditPresets();
                        }
                    }
                });
            }

            var show = new OptionsPresenter(OptionPresets);
            var selected = await show.ShowGetOne(ResStrings.Format);
            selected?.Switched?.Invoke(true);
        }

        private ViewfinderPreset _editingPreset;

        public async void StartEditPresets()
        {
            EditPreset(SelectedPresetInfo);
            return;

            OptionEditPresets.Clear();
            if (!OptionEditPresets.Any())
            {
                foreach (var preset in App.ViewFinderData.Presets)
                {
                    OptionEditPresets.Add(new OptionsListItem
                    {
                        Tag = preset.Id,
                        ActionDesc = preset.Title,
                        Switched = (e) =>
                        {
                            _editingPreset = preset;
                            EditPreset(preset);
                        }
                    });
                }
            }

            var show = new OptionsPresenter(OptionEditPresets);
            var selected = await show.ShowGetOne(ResStrings.EditPresets);
            selected?.Switched?.Invoke(true);

        }


        public void EditPreset(ViewfinderPreset preset)
        {
            var buidList = new List<OptionItem>();

            var lenses = new List<FocalLengthItem>();
            foreach (var lens in preset.CameraLens.TagsToIntList())
            {
                var add = new FocalLengthItem
                {
                    Title = $"{lens} mm",
                    Id = $"{lens}"
                };
                lenses.Add(add);

                buidList.Add(new OptionItem
                {
                    Id = add.Id,
                    Title = add.Title
                });
            }

            var title = string.Format(ResStrings.LensesFor, preset.Title);

            EditableListViewDrawn.Show(new Command((object context) =>
            {
                var list = context as IEnumerable<OptionItem>;

                //todo apply changes
                if (list != null && preset != null)
                {
                    var focals = list.Select(x => x.Id).ToTags();
                    preset.CameraLens = focals;

                    //var existing = App.ViewFinderData.Presets.FirstOrDefault(x => x.Id == preset.Id);
                    App.ViewFinderData.SaveUserPresets(App.ViewFinderData.Presets);

                    if (CurrentPreset == preset.Id)
                    {
                        ApplyCurrentPreset();
                        //CycleFrames();
                    }
                }
            }), title, buidList);

            //var dialog = new EditableListView(new Command((object context) =>
            //    {
            //        var list = context as IEnumerable<OptionItem>;

            //        //todo apply changes
            //        if (list != null && preset != null)
            //        {
            //            var focals = list.Select(x => x.Id).ToTags();
            //            preset.CameraLens = focals;

            //            //var existing = App.ViewFinderData.Presets.FirstOrDefault(x => x.Id == preset.Id);
            //            App.ViewFinderData.SaveUserPresets(App.ViewFinderData.Presets);

            //            if (CurrentPreset == preset.Id)
            //            {
            //                ApplyCurrentPreset();
            //                //CycleFrames();
            //            }
            //        }

            //    }),
            //    title,
            //    buidList);

            //dialog.Open();

        }




        #endregion

        #region PROPS and FIELDS


        private bool _ShowReset;





        public bool ShowReset
        {
            get { return _ShowReset; }
            set
            {
                if (_ShowReset != value)
                {
                    _ShowReset = value;
                    OnPropertyChanged();
                }
            }
        }


        private bool _ShowPresets;
        public bool ShowPresets
        {
            get
            {
                if (!ShowSettings)
                    return false;

                return _ShowPresets;
            }
            set
            {
                if (_ShowPresets != value)
                {
                    _ShowPresets = value;
                    OnPropertyChanged();
                }
            }
        }



        private bool _FogEnabled;
        public bool FogEnabled
        {
            get { return _FogEnabled; }
            set
            {
                if (_FogEnabled != value)
                {
                    _FogEnabled = value;
                    OnPropertyChanged();

                    Settings.Current.AddOrUpdateValue($"{this.GetType().Name}_Fog", value);
                }
            }
        }


        private string _currentPreset;
        public string CurrentPreset
        {
            get
            {
                return _currentPreset;
            }
            set
            {
                if (_currentPreset != value)
                {
                    _currentPreset = value;
                    OnPropertyChanged();

                    Settings.Current.AddOrUpdateValue($"{this.GetType().Name}_Preset", value);

                    if (value != null)
                    {
                        SelectedPresetInfo = App.ViewFinderData.Presets.FirstOrDefault(x => x.Id == value);
                    }

                }
            }
        }


        private string _ZoomDesc = "1x";
        public string ZoomDesc
        {
            get { return _ZoomDesc; }
            set
            {
                if (_ZoomDesc != value)
                {
                    _ZoomDesc = value;
                    OnPropertyChanged();
                }
            }
        }


#if DEBUG
        private bool _IsDebug = true;
#else
        private bool _IsDebug;
#endif
        public bool IsDebug
        {
            get { return _IsDebug; }
            set
            {
                if (_IsDebug != value)
                {
                    _IsDebug = value;
                    OnPropertyChanged();
                }
            }
        }

        private MainVModel Model { get; set; }


        public double CurrentFocalLength35mm
        {
            get
            {
                if (Camera?.CameraDevice == null)
                    return 0;

                return Camera.FocalLengthAdjusted;
            }
        }

        private static string SvgMono;
        private static string SvgColor;

        //we are using this instead of triggers since they bug at start
        //included content triggers not working for some reason at startup
        public string IconEffect
        {
            get
            {
                if (Camera == null)
                    return string.Empty;

                if (SvgMono == null)
                {
                    SvgColor = App.Current.Resources.Get<string>("SvgCamPresetColor");
                    SvgMono = App.Current.Resources.Get<string>("SvgCamPresetMono");
                }

                switch (Camera.Filter)
                {
                case CameraEffect.Grayscale:
                case CameraEffect.GrayscaleNegative:
                return SvgColor;
                default:
                return SvgMono;
                }
            }
        }

        private CameraFormatWithLens _currentFormatWithLens;
        public CameraFormatWithLens CurrentFormat
        {
            get { return _currentFormatWithLens; }
            set
            {
                if (_currentFormatWithLens != value)
                {
                    _currentFormatWithLens = value;
                    OnPropertyChanged();
                }
            }
        }


        public NiftyObservableCollection<PresetViewport> PresetFrames { get; } = new NiftyObservableCollection<PresetViewport>();

        bool lockStartup;


        public double ButtonsSpacing
        {
            get
            {
                var pts = DeviceDisplay.MainDisplayInfo.Width / Core.DisplayDensity;
                Debug.WriteLine($"-aaa- {pts}");
                if (pts < 390)
                {
                    return 20.0;
                }
                return 30.0;
            }
        }


        private bool _ShowSettings;
        public bool ShowSettings
        {
            get { return _ShowSettings; }
            set
            {
                if (_ShowSettings != value)
                {
                    _ShowSettings = value;
                    OnPropertyChanged();
                    //       OnPropertyChanged("ShowPresets");
                }
            }
        }


        private bool _PermissionsWarning;
        public bool PermissionsWarning
        {
            get { return _PermissionsWarning; }
            set
            {
                if (_PermissionsWarning != value)
                {
                    _PermissionsWarning = value;
                    OnPropertyChanged();
                }
            }
        }


        #endregion

        #region CAMERA


        //not localized cuz not used in prod
        public string ModeDesc
        {
            get
            {
                switch (Camera.Filter)
                {
                case CameraEffect.GrayscaleNegative:
                return "ЧБ";
                case CameraEffect.ColorNegativeAuto:
                return "Авто";
                case CameraEffect.ColorNegativeManual:
                return "Вручную";
                }
                return "";
            }
            set
            {

            }
        }

        private void OnTapped_BlackenFrames(object sender, TapEventArgs e)
        {
            FogEnabled = !FogEnabled;
        }


        private static int filenamesCounter = 0;



        public bool ShowPickers
        {
            get
            {
                return Camera.Filter == CameraEffect.ColorNegativeManual && Camera.PickerMode == CameraPickerMode.None;
            }
        }


        private void OnTapped_Reset(object sender, TapEventArgs e)
        {
            SetGamma(1.0);
        }


        private void OnTapped_Filter(object? sender, SkiaControl.ControlTappedEventArgs controlTappedEventArgs)
        {
            //            Camera.BlackColor = Colors.Black;

            if (Camera.Filter != CameraEffect.Grayscale)
            {
                SetEffect(CameraEffect.Grayscale);
                return;
            }

            SetEffect(CameraEffect.Positive);
        }







        #endregion

        void SetGamma(double value)
        {
            Camera.Gamma = value;
            if (value == 1.0)
                ShowReset = false;
            else
                ShowReset = true;

            Settings.Current.AddOrUpdateValue("CameraGamma", Camera.Gamma);
        }




        private void OnTapped_Focal(object sender, TapEventArgs e)
        {
            if (!PresetFrames.Any())
            {
                StartEditPresets();
                return;
            }
            CycleFrames();
            //Camera.FocalLength = 200;
        }

        private double lastCycledFrame;



        private void OnTapped_FocalPreset(object sender, TapEventArgs e)
        {
            PickAndSetPreset();
        }


        private void OnTapped_Plus(object sender, TapEventArgs e)
        {
            Camera.FocalLengthAdjustment += 1;
            //CycleFrames();
            //          SetGamma(Camera.Gamma + 0.1);
            Settings.Current.AddOrUpdateValue($"Adjustment_{CurrentFormat.Id}", Camera.FocalLengthAdjustment);
            CycleFrames(false);
        }

        private void OnTapped_Minus(object sender, TapEventArgs e)
        {
            Camera.FocalLengthAdjustment -= 1;
            //            SetGamma(Camera.Gamma - 0.1);
            Settings.Current.AddOrUpdateValue($"Adjustment_{CurrentFormat.Id}", Camera.FocalLengthAdjustment);
            CycleFrames(false);
        }


        private void OnTapped_Lenses(object sender, TapEventArgs e)
        {
            StartEditPresets();
        }
    }



}
