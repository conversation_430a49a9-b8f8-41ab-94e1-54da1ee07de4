﻿using System.Diagnostics;
using AppoMobi.Main;
using AppoMobi.Nifty;
using AppoMobi.Xam;

namespace AppoMobi.Main
{
    public class ContentCameraExposure : ScreenCanvas
    {

        //public ContentCameraExposure()
        //{
        //    RenderingMode = RenderingModeType.Accelerated;
        //    HorizontalOptions = LayoutOptions.Fill;
        //    VerticalOptions = LayoutOptions.Fill;
        //    BackgroundColor = Colors.Black;

        //}

        public ContentCameraExposure(IPageEnhancedNav daddy)
        {
            RenderingMode = RenderingModeType.Accelerated;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.Black;

            Daddy = daddy;

            //TOOLBAR ICONS
            Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
            Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

            Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
            Daddy.ToggleButtonVisibility(ButtonType.Right2, true);


            WithCamera = new ExposureMeter(ExposureMeteringMode.EXIF);

            Content = WithCamera;

            if (Preferences.Get("WarnExp", true))
            {
                Preferences.Set("WarnExp", false);
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var dialog = new PopupResult(ResStrings.WarningExposure, ResStrings.ButtonOk);
                    await dialog.ShowAsync(false);
                });
            }
        }

        public override void OnRightIcon1Clicked()
        {
            Core.ShowHelp(ResStrings.HelpExposure);

            base.OnRightIcon1Clicked();
        }

        public override void OnRightIcon2Clicked()
        {
            WithCamera?.EditCorrection();

            base.OnRightIcon2Clicked();
        }

        public override void OnAppearing()
        {
            Debug.WriteLine($"OnAppearing {this}");

            WithCamera?.OnAppearing();

            base.OnAppearing();
        }

        public override void OnDisappearing()
        {
            Debug.WriteLine($"OnDisappearing {this}");

            base.OnDisappearing();

            WithCamera?.OnDisappearing();

            GC.Collect();
        }

        private ExposureMeter? WithCamera;

 
    }
}
