﻿using System.Diagnostics;
using Newtonsoft.Json;
using LayoutAlignment = Microsoft.Maui.Primitives.LayoutAlignment;

namespace AppoMobi.Main
{
    public class TimerStep
    {
        public static void Save(List<TimerStep> value)
        {
            var jsonPresets = JsonConvert.SerializeObject(value);
            Settings.Current.AddOrUpdateValue($"TimerSteps", jsonPresets);
        }

        public static List<TimerStep> Load()
        {
            var jsonPresets = Settings.Current.GetValueOrDefault($"TimerSteps", string.Empty);
            if (!string.IsNullOrEmpty(jsonPresets))
            {
                try
                {
                    return JsonConvert.DeserializeObject<List<TimerStep>>(jsonPresets);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }

            return null;
        }

        public string Title { get; set; }
        public double Time { get; set; }
        public string Icon { get; set; }
    }

    public class StepsTimer : AppScreen
    {
        public StepsTimer()
        {
            BackgroundColor = Colors.Black;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            Children = new List<SkiaControl>()
            {
                new SkiaLayout()
                {
                    VerticalOptions = LayoutOptions.Center,
                    Type = LayoutType.Column,
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        //new SkiaLabel(
                        //    "Модуль предназначен для..... При открытом столе нажимайте на область в правом верхнем углу для показа и скрытия настроек.")
                        //{
                        //    WidthRequest = 250,
                        //    HorizontalOptions = LayoutOptions.Center,
                        //    UseCache = SkiaCacheType.Operations,
                        //    FontFamily = "FontText",
                        //    //TextColor = Colors.Black,
                        //    //FontSize = 80
                        //},

                        new SkiaStepper()
                        {
                            CurrentStep = 1,
                            Margin = 32,
                            HeightRequest = 24,
                            HorizontalOptions = LayoutOptions.Fill,
                            ThumbColor = Colors.Orange,
                            TotalSteps = 3,
                            TrackBackgroundColor = Colors.DimGray,
                            TrackForegroundColor = Colors.White
                        },
                        new AppButton(ResStrings.StartUp)
                        {
                            UseCache = SkiaCacheType.Image, HorizontalOptions = LayoutOptions.Center, Margin = 16
                        }.OnTapped((me) =>
                        {
                            Debug.WriteLine("OPEN");

                            var wrapper = new ScreenCanvas()
                            {
                                RenderingMode = RenderingModeType.Accelerated,
                                Content = new LightPadPopup(),
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalOptions = LayoutOptions.Fill
                            };

                            var popup = new CustomPopup()
                            {
                                HorizontalOptions = LayoutAlignment.Fill,
                                VerticalOptions = LayoutAlignment.Fill,
                                IgnoreSafeArea = true,
                                Content = new DisposableContent() { Content = wrapper }
                            };

                            MainThread.BeginInvokeOnMainThread(() => { popup.Open(true); });
                        })
                    }
                }
            };
        }
    }
}
