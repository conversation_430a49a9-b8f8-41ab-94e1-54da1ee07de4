﻿using System.Diagnostics;
using AppoMobi.Main;
using AppoMobi.Nifty;
using AppoMobi.Xam;

namespace AppoMobi.Main
{
    public class ContentCameraSpot : ScreenCanvas
    {

        public ContentCameraSpot()
        {
            RenderingMode = RenderingModeType.Accelerated;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.Black;


            WithCamera = new ExposureMeter(ExposureMeteringMode.Spot);
            Content = WithCamera;

            if (Preferences.Get("WarnSpot", true))
            {
                Preferences.Set("WarnSpot", false);
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var dialog = new PopupResult(ResStrings.WarningSpot, ResStrings.ButtonOk);
                    await dialog.ShowAsync(false);
                });
            }
        }

        public ContentCameraSpot(IPageEnhancedNav daddy)
        {
            RenderingMode = RenderingModeType.Accelerated;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.Black;

            Daddy = daddy;

            //TOOLBAR ICONS
            Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
            Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

            Daddy.RightIcon2Symbol.SetIcon(FontIcons.fa_cog);
            Daddy.ToggleButtonVisibility(ButtonType.Right2, true);

            WithCamera = new ExposureMeter(ExposureMeteringMode.Spot);
            Content = WithCamera;

            if (Preferences.Get("WarnSpot", true))
            {
                Preferences.Set("WarnSpot", false);
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var dialog = new PopupResult(ResStrings.WarningSpot, ResStrings.ButtonOk);
                    await dialog.ShowAsync(false);
                });
            }
        }

        public override void OnRightIcon1Clicked()
        {
            Core.ShowHelp(ResStrings.HelpSpot);

            base.OnRightIcon1Clicked();
        }

        public override void OnRightIcon2Clicked()
        {
            WithCamera?.EditCorrection();

            base.OnRightIcon2Clicked();
        }

        public override void OnAppearing()
        {
            Debug.WriteLine($"OnAppearing {this}");

            WithCamera?.OnAppearing();

            base.OnAppearing();
        }

        public override void OnDisappearing()
        {
            Debug.WriteLine($"OnDisappearing {this}");

            base.OnDisappearing();

            WithCamera?.OnDisappearing();

            GC.Collect();
        }

        private ExposureMeter? WithCamera;

 
    }
}
