﻿<?xml version="1.0" encoding="utf-8"?>

<pages:IncludedContent
    x:Class="AppoMobi.Pages.ContentReciprocity"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="using:AppoMobi.Maui.DrawnUi.Demo.Views.Controls"
    xmlns:converters="clr-namespace:AppoMobi.Xam.Converters"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:viewModels="using:AppoMobi.ViewModels"
    xmlns:views="clr-namespace:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    xmlns:mobile="using:AppoMobi.Mobile"
    x:Name="ThisPage"
    x:DataType="viewModels:ViewModelReciprocity"
    HorizontalOptions="FillAndExpand">

    <draw:Canvas
        BackgroundColor="Black"
        Gestures="Lock"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"

        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill" VerticalOptions="Fill">

            <draw:SkiaImage
                Opacity="{x:Static mobile:MauiProgram.WallpaperOpacity}"
                UseCache="Image"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                Source="Images\back.jpg" />

            <draw:SkiaLayout.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Offset="0.0" Color="{x:Static xam:BackColors.GradientPageFaderStart}" />
                    <GradientStop Offset="1.0" Color="{x:Static xam:BackColors.GradientPageFaderStartEnd}" />
                </LinearGradientBrush>
            </draw:SkiaLayout.Background>

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Spacing="16"
                Type="Column"
                VerticalOptions="Start">

                <!--  STYLED SELECTOR FILM  -->
                <draw:SkiaShape
                    Margin="25,25,25,5"
                    AnimationTapped="Ripple"
                    BackgroundColor="Transparent"
                    CornerRadius="7"
                    HorizontalOptions="Fill"
                    StrokeColor="#DD999999"
                    StrokeWidth="1"
                    Tag="Stroked"
                    HeightRequest="36"
                    Tapped="TappedSelectorFilm"
                    TransformView="{x:Reference FilmGradient}"
                    UseCache="Image">

                    <draw:SkiaLayer UseCache="Operations">

                        <draw:SkiaLayout
                            x:Name="FilmGradient"
                            BackgroundColor="Red"
                            FillGradient="{x:Static views:Elements.ControlGradient}"
                            HorizontalOptions="Fill"
                            IsVisible="True"
                            Opacity="0.25"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                        <draw:SkiaLabel
                            Margin="8,7,8,9"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{Binding SelectedFilm}"
                            TextColor="{x:Static xam:TextColors.PlaceholderActive}"
                            VerticalOptions="Center" />

                        <views:DrawnFontIcon
                            Margin="0,0,8,0"
                            FontSize="14"
                            HorizontalOptions="End"
                            InputTransparent="True"
                            Opacity="0.3"
                            Text="{x:Static xam:FaPro.CircleDown}"
                            TextColor="{x:Static xam:TextColors.Entry}"
                            VerticalOptions="Center" />

                    </draw:SkiaLayer>


                </draw:SkiaShape>

                <!--  STYLED SELECTOR FILTER  -->
                <draw:SkiaShape
                    Margin="25,5,25,5"
                    AnimationTapped="Ripple"
                    BackgroundColor="Transparent"
                    CornerRadius="7"
                    HorizontalOptions="Fill"
                    StrokeColor="#DD999999"
                    StrokeWidth="1"
                    Tag="Stroked"
                    Tapped="TappedSelectorFIlter"
                    TransformView="{x:Reference FilterGradient}"
                    UseCache="Image">


                    <draw:SkiaLayer UseCache="Operations">

                        <draw:SkiaLayout
                            x:Name="FilterGradient"
                            BackgroundColor="Red"
                            FillGradient="{x:Static views:Elements.ControlGradient}"
                            HorizontalOptions="Fill"
                            IsVisible="True"
                            Opacity="0.25"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                        <draw:SkiaLabel
                            Margin="8,7,8,9"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{Binding SelectedFilter}"
                            TextColor="{x:Static xam:TextColors.PlaceholderActive}"
                            VerticalOptions="Center" />

                        <views:DrawnFontIcon
                            Margin="0,0,8,0"
                            FontSize="14"
                            HorizontalOptions="End"
                            InputTransparent="True"
                            Opacity="0.3"
                            Text="{x:Static xam:FaPro.CircleDown}"
                            TextColor="{x:Static xam:TextColors.Entry}"
                            VerticalOptions="Center" />

                    </draw:SkiaLayer>


                </draw:SkiaShape>

                <!--  EXPOSURE WEELS  -->
                <!--  todo WillFirstTimeDraw="DrawnView_OnWillFirstTimeDraw"  -->
                <draw:SkiaLayout
                    Margin="20,5,0,0"
                    HeightRequest="200"
                    HorizontalOptions="Center"
                    Tag="WheelsContainer"
                    WidthRequest="250">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        Spacing="16"
                        Type="Row"
                        VerticalOptions="Fill">

                        <!--
                          SelectedIndex="{Binding SelectedMinutesIndex, Mode=OneWay}"
                          DataSource="{Binding ExposureMinutesSource}"
                          IndexChangedCommand="{Binding ExposureMinutesSelectedCommand}"
                      -->

                        <controls:WheelPicker
                            x:Name="Picker"
                            Grid.Column="0"
                            DataSource="{Binding ExposureMinutesSource}"
                            HeightRequest="-1"
                            HorizontalOptions="Start"
                            IndexChangedCommand="{Binding ExposureMinutesSelectedCommand}"
                            IsVisible="True"
                            LinesColor="{x:Static xam:BackColors.NavBarDropShadow}"
                            SelectedIndex="{Binding SelectedMinutesIndex}"
                            TextColor="{x:Static xam:TextColors.Result}"
                            TextSelectedColor="{x:Static xam:TextColors.Result}"
                            UseCache="Operations"
                            VerticalOptions="Fill"
                            WidthRequest="75">
                            <!--<controls:WheelPicker.DataSource>
                          <x:Array Type="{x:Type x:String}">
                              <x:String>1</x:String>
                              <x:String>2</x:String>
                              <x:String>3</x:String>
                              <x:String>4</x:String>
                              <x:String>5</x:String>
                              <x:String>6</x:String>
                              <x:String>7</x:String>
                              <x:String>8</x:String>
                              <x:String>9</x:String>
                              <x:String>10</x:String>
                              <x:String>11</x:String>
                              <x:String>12</x:String>
                          </x:Array>
                      </controls:WheelPicker.DataSource>-->
                        </controls:WheelPicker>

                        <draw:SkiaLabel
                            Margin="-6,0,0,0"
                            FontSize="11"
                            HorizontalOptions="Start"
                            Text="{x:Static resX:ResStrings.X_Mins}"
                            TextColor="{x:Static xam:TextColors.EntryDesc}"
                            UseCache="Image"
                            VerticalOptions="Center" />

                        <!--
                          DataSource="{Binding ExposureSecondsSource}"
                          SelectedIndex="{Binding SelectedSecondsIndex, Mode=OneWay}"
                          IndexChangedCommand="{Binding ExposureSecondsSelectedCommand}"
                      -->

                        <controls:WheelPicker
                            Grid.Column="2"
                            DataSource="{Binding ExposureSecondsSource}"
                            HeightRequest="-1"
                            HorizontalOptions="Start"
                            IndexChangedCommand="{Binding ExposureSecondsSelectedCommand}"
                            LinesColor="{x:Static xam:BackColors.NavBarDropShadow}"
                            SelectedIndex="{Binding SelectedSecondsIndex}"
                            TextColor="{x:Static xam:TextColors.Result}"
                            TextSelectedColor="{x:Static xam:TextColors.Result}"
                            UseCache="Operations"
                            VerticalOptions="Fill"
                            WidthRequest="100" />


                        <draw:SkiaLabel
                            Margin="-6,0,0,0"
                            FontSize="11"
                            HorizontalOptions="Start"
                            Text="{x:Static resX:ResStrings.X_Secs}"
                            TextColor="{x:Static xam:TextColors.EntryDesc}"
                            UseCache="Image"
                            VerticalOptions="Center" />

                    </draw:SkiaLayout>


                </draw:SkiaLayout>


                <!--  RESULT  -->
                <draw:SkiaLayout HorizontalOptions="Fill">

                    <draw:SkiaLayout
                        HeightRequest="90"
                        Padding="10,0"
                        Margin="10"
                        HorizontalOptions="Center"
                        InputTransparent="True"
                        Spacing="4"
                        Type="Column">

                        <draw:SkiaLabel
                            FontSize="22"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            Text="{x:Static resX:ResStrings.X_AdjustedTime}"
                            TextColor="{x:Static xam:TextColors.EntryDesc}"
                            UseCache="Operations" />

                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="55"
                            HorizontalOptions="Center"
                            MaxLines="1"
                            Text="{Binding ResultDesc}"
                            TextColor="{x:Static xam:TextColors.Result}"
                            UseCache="Operations"
                            VerticalOptions="Start"
                            VerticalTextAlignment="Center" />

                    </draw:SkiaLayout>

                    <draw:SkiaLayout
                        x:Name="BtnTimer"
                        Margin="0,10,20,0"
                        HorizontalOptions="End"
                        IsVisible="{Binding ResultMs, Converter={converters:DoubleOverZeroConverter}}"
                        Tapped="TappedTimer"
                        UseCache="Image">

                        <views:DrawnFontIcon
                            FontSize="30"
                            InputTransparent="True"
                            Opacity="0.75"
                            Text="{x:Static xam:FaPro.Stopwatch}"
                            TextColor="{x:Static xam:TextColors.EntryDesc}" />

                    </draw:SkiaLayout>

                </draw:SkiaLayout>

                <!--  FILM NOTES  -->
                <draw:SkiaLabel
                    Margin="0,10,0,0"
                    FontSize="12"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    IsVisible="{Binding FilmNotes, Converter={converters:StringNotEmptyConverter}}"
                    LineBreakMode="WordWrap"
                    Text="{Binding FilmNotes}"
                    TextColor="{x:Static xam:TextColors.Result}"
                    UseCache="Image"
                    WidthRequest="200" />

                <!--  MODULE DESC  -->
                <draw:SkiaLabel
                    Margin="0,10,0,0"
                    FontSize="12"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    IsVisible="{Binding ModuleDesc, Converter={converters:StringNotEmptyConverter}}"
                    LineBreakMode="WordWrap"
                    Text="{Binding ModuleDesc}"
                    TextColor="{x:Static xam:TextColors.EntryDesc}"
                    UseCache="Image"
                    WidthRequest="200" />

                <!--<draw:SkiaControl
                    x:Name="bottomPadding"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start" />-->

            </draw:SkiaLayout>

            <!--  FPS  -->

            <draw:SkiaLabelFps
                IsVisible="{Binding IsDebug}"
                Margin="0,0,4,28"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End" />

        </draw:SkiaLayout>

    </draw:Canvas>

</pages:IncludedContent>